{"name": "shopery", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-free": "^7.0.0", "@hookform/resolvers": "^5.2.1", "@reduxjs/toolkit": "^2.8.2", "@tanstack/react-query": "^5.87.4", "@tanstack/react-query-devtools": "^5.87.4", "axios": "^1.11.0", "framer-motion": "^12.23.12", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.62.0", "react-redux": "^9.2.0", "react-router-dom": "^7.8.0", "react-toastify": "^11.0.5", "yup": "^1.7.0", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.4"}}