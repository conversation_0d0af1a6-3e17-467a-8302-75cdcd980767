/* Exam Progress Component Styles */
.exam-progress {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #E5E7EB;
}

.exam-progress__container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Header Info */
.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.progress-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.75rem 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #E5E7EB;
  min-height: 60px;
  justify-content: center;
  flex: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.question-info {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1F2937;
}

.question-current {
  color: #4F46E5;
}

.question-separator {
  color: #6B7280;
}

.question-total {
  color: #6B7280;
}

.time-info {
  font-size: 1.5rem;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.answered-info {
  font-size: 1.5rem;
  font-weight: 700;
  color: #10B981;
}

.progress-label {
  font-size: 0.875rem;
  color: #6B7280;
  font-weight: 500;
}

/* Progress Bars */
.progress-bars {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.progress-bar-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0.75rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #E5E7EB;
}

.progress-bar-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #E5E7EB;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4F46E5, #7C3AED);
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill.time-progress {
  background: linear-gradient(90deg, #10B981, #059669);
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-percentage {
  font-size: 0.75rem;
  color: #6B7280;
  font-weight: 600;
  text-align: right;
}

/* Action Buttons */
.progress-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  max-width: 120px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pause-btn {
  background: #F59E0B;
  color: white;
}

.pause-btn:hover {
  background: #D97706;
  transform: translateY(-1px);
}

.resume-btn {
  background: #10B981;
  color: white;
}

.resume-btn:hover {
  background: #059669;
  transform: translateY(-1px);
}

.exit-btn {
  background: #EF4444;
  color: white;
}

.exit-btn:hover {
  background: #DC2626;
  transform: translateY(-1px);
}


/* Responsive */
@media (max-width: 768px) {
  .exam-progress {
    padding: 1rem;
  }

  .progress-header {
    flex-direction: column;
    gap: 0.75rem;
  }

  .progress-bars {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }


  .progress-actions {
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
    max-width: none;
    justify-content: center;
  }

  .question-info,
  .time-info,
  .answered-info {
    font-size: 1.25rem;
  }
}

/* Animation for time warning */
.time-info.warning {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}
