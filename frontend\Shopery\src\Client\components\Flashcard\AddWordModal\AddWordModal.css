/* Client/components/Flashcard/AddWordModal/AddWordModal.css */
.add-word-modal {
    max-width: 700px;
  }
  
  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }
  
  .form-group select {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 16px;
    background: white;
    cursor: pointer;
  }
  
  .form-group select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
  
  .form-group input[type="file"] {
    padding: 8px;
    border: 1px dashed #d1d5db;
    border-radius: 8px;
    background: #f9fafb;
    cursor: pointer;
  }
  
  .form-group input[type="file"]:hover {
    border-color: #3b82f6;
    background: #eff6ff;
  }
  
  @media (max-width: 768px) {
    .form-row {
      grid-template-columns: 1fr;
    }
  }