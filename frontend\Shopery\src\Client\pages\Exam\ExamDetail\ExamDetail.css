/* Exam Detail Page Styles */
.exam-detail {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 2rem 0;
}

.exam-detail__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Loading State */
.exam-detail-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  color: #181818;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #E5E7EB;
  border-top: 4px solid #1F2937;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.exam-detail-error {
  text-align: center;
  color: #181818;
  padding: 4rem 2rem;
}

.exam-detail-error h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.exam-detail-error p {
  margin-bottom: 2rem;
  color: #888;
}

/* Header */
.exam-detail__header {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 3rem;
}

.exam-detail__image {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  height: 200px;
}

.exam-detail__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.exam-detail__type {
  position: absolute;
  top: 1rem;
  left: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  color: #181818;
  font-weight: 700;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.exam-detail__info {
  color: #181818;
}

.exam-detail__title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.exam-detail__description {
  font-size: 1.125rem;
  color: #888;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.exam-detail__meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
}

.meta-item svg {
  color: #888;
}

.difficulty-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  color: #181818;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.exam-detail__tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.tag {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.2);
  color: #181818;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.exam-detail__score {
  display: flex;
  gap: 2rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.score-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.score-label {
  font-size: 0.875rem;
  color: #888;
}

.score-value {
  font-size: 1.25rem;
  font-weight: 700;
}

/* Sections */
.exam-detail__sections {
  margin-bottom: 3rem;
}

.sections-title {
  font-size: 2rem;
  font-weight: 700;
  color: #181818;
  text-align: center;
  margin-bottom: 2rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.sections-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.section-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.section-card:hover {
  transform: translateY(-4px);
}

.section-card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #F3F4F6;
}

.section-card__title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #181818;
}

.section-card__meta {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #888;
}

.duration {
  font-weight: 600;
  color: #181818;
}

.questions {
  font-weight: 600;
  color: #059669;
}

.section-card__description {
  color: #888;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.section-card__parts {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.part-item {
  padding: 1rem;
  background: #F9FAFB;
  border-radius: 8px;
  border-left: 4px solid #1F2937;
}

.part-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.part-name {
  font-weight: 600;
  color: #181818;
}

.part-questions {
  font-size: 0.875rem;
  color: #888;
  background: #E5E7EB;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
}

.part-description {
  font-size: 0.875rem;
  color: #888;
  line-height: 1.5;
}

/* Instructions */
.exam-detail__instructions {
  margin-bottom: 3rem;
}

.instructions-title {
  font-size: 2rem;
  font-weight: 700;
  color: #181818;
  text-align: center;
  margin-bottom: 2rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.instructions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.instruction-section {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 12px rgba(0,0,0,0.06);
}

.instruction-section h3 {
  color: #181818;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.instruction-section ul {
  list-style: none;
  padding: 0;
}

.instruction-section li {
  color: #181818;
  line-height: 1.6;
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
  position: relative;
}

.instruction-section li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #10B981;
  font-weight: bold;
}

/* Actions */
.exam-detail__actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  cursor: pointer;
  font-size: 1rem;
}

.btn-large {
  padding: 1.25rem 2.5rem;
  font-size: 1.125rem;
}

.btn-primary {
  background: white;
  color: #181818;
  border-color: #181818;
}

.btn-primary:hover {
  background: #1F2937;
  color: #181818;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

.btn-secondary {
  background: transparent;
  color: #181818;
  border-color: rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* Responsive */
@media (max-width: 768px) {
  .exam-detail__container {
    padding: 0 0.5rem;
  }

  .exam-detail__header {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .exam-detail__title {
    font-size: 2rem;
  }

  .exam-detail__description {
    font-size: 1rem;
  }

  .exam-detail__meta {
    gap: 1rem;
  }

  .sections-grid,
  .instructions-grid {
    grid-template-columns: 1fr;
  }

  .section-card {
    padding: 1.5rem;
  }

  .instruction-section {
    padding: 1.5rem;
  }

  .exam-detail__actions {
    flex-direction: column;
    align-items: center;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    max-width: 300px;
  }
}
