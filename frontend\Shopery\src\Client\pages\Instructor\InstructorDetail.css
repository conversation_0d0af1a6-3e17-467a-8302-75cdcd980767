/* Instructor Detail Page - CSS theo design system và layout 2 cột */
:root {
  --main-black: #181818;
  --main-gray: #888;
  --main-orange: #ff9800;
  --main-yellow: #ffc107;
  --main-border: #e5e7eb;
  --main-bg: #fafbfc;
  --main-white: #fff;
  --main-radius: 18px;
  --main-shadow: 0 2px 12px rgba(0,0,0,0.06);
  
  /* Instructor specific colors */
  --instructor-primary: #4fd1c7;
  --instructor-accent: #FF6B35;
  --instructor-gradient: linear-gradient(135deg, #4fd1c7 0%, #38b2ac 100%);
  --instructor-light: #f0f9ff;
}

.instructor-detail-page {
  min-height: 100vh;
  background: var(--main-bg);
  padding: 90px 0 2rem 0; /* Tránh header fixed */
}

.instructor-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Breadcrumb */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 32px;
  font-size: 0.9rem;
  color: var(--main-gray);
}

.breadcrumb-link {
  color: var(--instructor-primary);
  text-decoration: none;
  transition: color 0.3s ease;
}

.breadcrumb-link:hover {
  color: var(--instructor-accent);
}

.breadcrumb-separator {
  color: var(--main-gray);
}

/* Main Content Layout */
.instructor-detail-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 40px;
  align-items: start;
}

/* Left Column - Instructor Profile */
.instructor-profile {
  background: var(--main-white);
  border-radius: var(--main-radius);
  padding: 32px;
  box-shadow: var(--main-shadow);
  border: 1px solid var(--main-border);
  position: sticky;
  top: 110px;
}

.instructor-avatar {
  width: 200px;
  height: 200px;
  margin: 0 auto 24px;
  border-radius: 12px;
  overflow: hidden;
  border: 3px solid var(--instructor-light);
}

.instructor-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.instructor-basic-info {
  text-align: center;
  margin-bottom: 32px;
}

.instructor-name {
  font-size: 2rem;
  font-weight: 700;
  color: var(--main-black);
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.instructor-title {
  font-size: 1.1rem;
  color: var(--main-gray);
  margin: 0 0 16px 0;
  font-weight: 500;
}

.instructor-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.stars {
  display: flex;
  gap: 4px;
}

.stars i {
  font-size: 1.1rem;
  color: #e4e5e9;
  transition: color 0.2s ease;
}

.stars i.filled {
  color: var(--main-yellow);
}

.rating-text {
  font-size: 1rem;
  color: var(--main-gray);
  font-weight: 500;
}

/* Instructor Stats */
.instructor-stats {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--instructor-light);
  border-radius: 12px;
  border: 1px solid rgba(79, 209, 199, 0.2);
}

.stat-icon {
  width: 48px;
  height: 48px;
  background: var(--instructor-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--main-black);
  line-height: 1.2;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--main-gray);
  font-weight: 500;
}

/* Right Column - About & Courses */
.instructor-details {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

/* Section Titles */
.section-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--main-black);
  margin: 0 0 24px 0;
  line-height: 1.3;
}

/* About Section */
.about-section {
  background: var(--main-white);
  border-radius: var(--main-radius);
  padding: 32px;
  box-shadow: var(--main-shadow);
  border: 1px solid var(--main-border);
}

.about-content p {
  font-size: 1rem;
  line-height: 1.7;
  color: var(--main-black);
  margin: 0;
}

/* Courses Section */
.courses-section {
  background: var(--main-white);
  border-radius: var(--main-radius);
  padding: 32px;
  box-shadow: var(--main-shadow);
  border: 1px solid var(--main-border);
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

/* Course Card */
.course-card {
  background: var(--main-white);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--main-border);
  transition: all 0.3s ease;
}

.course-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: var(--instructor-primary);
}

.course-image {
  position: relative;
  width: 100%;
  height: 180px;
  overflow: hidden;
}

.course-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.course-card:hover .course-image img {
  transform: scale(1.05);
}

.course-category {
  position: absolute;
  top: 12px;
  left: 12px;
  background: var(--instructor-primary);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.course-content {
  padding: 20px;
}

.course-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--main-black);
  margin: 0 0 12px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-rating {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.course-rating .stars {
  gap: 2px;
}

.course-rating .stars i {
  font-size: 0.9rem;
}

.course-rating .rating-text {
  font-size: 0.85rem;
}

.course-meta {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  font-size: 0.85rem;
  color: var(--main-gray);
}

.course-lessons,
.course-duration {
  display: flex;
  align-items: center;
  gap: 4px;
}

.course-author {
  font-size: 0.85rem;
  color: var(--main-gray);
  margin-bottom: 16px;
  font-weight: 500;
}

.course-price {
  display: flex;
  align-items: center;
  gap: 12px;
}

.current-price {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--instructor-accent);
}

.original-price {
  font-size: 0.9rem;
  color: var(--main-gray);
  text-decoration: line-through;
}

/* Not Found */
.not-found {
  text-align: center;
  padding: 60px 20px;
}

.not-found h2 {
  font-size: 1.5rem;
  color: var(--main-black);
  margin-bottom: 20px;
}

.back-link {
  display: inline-block;
  padding: 12px 24px;
  background: var(--instructor-primary);
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  transition: background 0.3s ease;
}

.back-link:hover {
  background: var(--instructor-accent);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .instructor-detail-content {
    grid-template-columns: 1fr;
    gap: 32px;
  }
  
  .instructor-profile {
    position: static;
    order: 2;
  }
  
  .instructor-details {
    order: 1;
  }
  
  .courses-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .instructor-detail-page {
    padding: 80px 0 1rem 0;
  }
  
  .instructor-container {
    padding: 0 16px;
  }
  
  .instructor-profile {
    padding: 24px;
  }
  
  .instructor-avatar {
    width: 150px;
    height: 150px;
  }
  
  .instructor-name {
    font-size: 1.6rem;
  }
  
  .about-section,
  .courses-section {
    padding: 24px;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
  
  .courses-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .instructor-stats {
    gap: 16px;
  }
  
  .stat-item {
    padding: 12px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .stat-number {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .instructor-detail-page {
    padding: 75px 0 1rem 0;
  }
  
  .instructor-profile {
    padding: 20px;
  }
  
  .instructor-avatar {
    width: 120px;
    height: 120px;
  }
  
  .instructor-name {
    font-size: 1.4rem;
  }
  
  .about-section,
  .courses-section {
    padding: 20px;
  }
  
  .section-title {
    font-size: 1.3rem;
  }
  
  .course-content {
    padding: 16px;
  }
  
  .course-image {
    height: 160px;
  }
}

/* Animation cho course cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.course-card {
  animation: fadeInUp 0.6s ease-out;
}

.course-card:nth-child(1) { animation-delay: 0.1s; }
.course-card:nth-child(2) { animation-delay: 0.2s; }
.course-card:nth-child(3) { animation-delay: 0.3s; }
.course-card:nth-child(4) { animation-delay: 0.4s; }
.course-card:nth-child(5) { animation-delay: 0.5s; }
.course-card:nth-child(6) { animation-delay: 0.6s; }
.course-card:nth-child(7) { animation-delay: 0.7s; }
.course-card:nth-child(8) { animation-delay: 0.8s; }
.course-card:nth-child(9) { animation-delay: 0.9s; }

/* Hover effects cho course cards */
.course-card {
  position: relative;
  overflow: hidden;
}

.course-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(79, 209, 199, 0.1), transparent);
  transition: left 0.5s ease;
  z-index: 1;
}

.course-card:hover::before {
  left: 100%;
}

/* Focus states cho accessibility */
.breadcrumb-link:focus,
.back-link:focus {
  outline: 2px solid var(--instructor-primary);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .instructor-detail-page {
    background: white;
    padding: 0;
  }
  
  .instructor-profile,
  .about-section,
  .courses-section {
    box-shadow: none;
    border: 1px solid #ccc;
  }
  
  .course-card {
    break-inside: avoid;
  }
}
