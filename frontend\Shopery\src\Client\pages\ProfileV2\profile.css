.profilev2-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px 16px;
}

.profilev2-header {
  display: flex;
  gap: 16px;
  align-items: center;
  padding: 24px;
  background: #0f172a;
  color: #fff;
  border-radius: 16px;
}

.profilev2-avatar {
  width: 72px;
  height: 72px;
  border-radius: 50%;
  overflow: hidden;
  background: #1f2937;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 24px;
}

.profilev2-content {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 16px;
  margin-top: 16px;
}

.profilev2-sidebar {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
}

.profilev2-menu {
  display: grid;
  gap: 8px;
}

.profilev2-menu button {
  text-align: left;
  border: 1px solid #e5e7eb;
  background: #f9fafb;
  padding: 10px 12px;
  border-radius: 8px;
  cursor: pointer;
}

.profilev2-menu button.active {
  background: #e0e7ff;
  border-color: #c7d2fe;
}

.profilev2-section {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
}

.section-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  padding: 16px;
  margin-bottom: 16px;
}

.form-row {
  display: grid;
  gap: 12px;
  margin-top: 8px;
}

.form-row input, .form-row select {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 10px 12px;
}

.actions {
  display: flex;
  gap: 10px;
  margin-top: 12px;
}

.btn-primary {
  background: #4f46e5;
  color: #fff;
  border: none;
  padding: 10px 14px;
  border-radius: 8px;
  cursor: pointer;
}

.btn-secondary {
  background: #4f46e5;
  color: #4f46e5;
  border: 1px solid #d1d5db;
  padding: 10px 14px;
  border-radius: 8px;
  cursor: pointer;
}

@media (max-width: 900px) {
  .profilev2-content {
    grid-template-columns: 1fr;
  }
}


