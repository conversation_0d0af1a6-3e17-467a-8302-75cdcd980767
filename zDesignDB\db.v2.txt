-- =============================================
-- TẠO DATABASE VÀ SỬ DỤNG
-- =============================================
CREATE DATABASE IF NOT EXISTS e_learnning2 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE e_learnning2;

-- =============================================
-- XÓA TOÀN BỘ BẢNG CŨ (THEO THỨ TỰ KHÓA NGOẠI)
-- =============================================
SET FOREIGN_KEY_CHECKS = 0;

DROP TABLE IF EXISTS learning_achievements;
DROP TABLE IF EXISTS study_notifications;
DROP TABLE IF EXISTS study_settings;
DROP TABLE IF EXISTS study_modes;
DROP TABLE IF EXISTS import_details;
DROP TABLE IF EXISTS batch_imports;
DROP TABLE IF EXISTS learning_statistics;
DROP TABLE IF EXISTS word_study_history;
DROP TABLE IF EXISTS session_details;
DROP TABLE IF EXISTS study_sessions;
DROP TABLE IF EXISTS favorite_topics;
DROP TABLE IF EXISTS word_learning_progress;
DROP TABLE IF EXISTS user_words;
DROP TABLE IF EXISTS words;
DROP TABLE IF EXISTS topics;
DROP TABLE IF EXISTS password_reset_tokens;
DROP TABLE IF EXISTS login_history;
DROP TABLE IF EXISTS email_verifications;
DROP TABLE IF EXISTS refresh_tokens;
DROP TABLE IF EXISTS otp_codes;
DROP TABLE IF EXISTS role_permissions;
DROP TABLE IF EXISTS permissions;
DROP TABLE IF EXISTS user_roles;
DROP TABLE IF EXISTS roles;
DROP TABLE IF EXISTS users;

SET FOREIGN_KEY_CHECKS = 1;

-- =============================================
-- TẠO BẢNG MỚI (CẤU TRÚC HOÀN CHỈNH)
-- =============================================

-- BẢNG USERS
CREATE TABLE users (
    user_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID người dùng',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT 'Tên đăng nhập',
    password_hash VARCHAR(255) NOT NULL COMMENT 'Mật khẩu đã mã hóa',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT 'Email duy nhất',
    full_name VARCHAR(100) COMMENT 'Họ tên đầy đủ',
    phone_number VARCHAR(20) COMMENT 'Số điện thoại',
    avatar_url VARCHAR(255) COMMENT 'Ảnh đại diện',
    status VARCHAR(20) DEFAULT 'active' COMMENT 'Trạng thái: active, inactive, banned, pending_verification',
    email_verified BOOLEAN DEFAULT FALSE COMMENT 'Email đã xác thực chưa',
    phone_verified BOOLEAN DEFAULT FALSE COMMENT 'Số điện thoại đã xác thực chưa',
    last_login DATETIME COMMENT 'Lần đăng nhập cuối',
    failed_login_attempts INT DEFAULT 0 COMMENT 'Số lần đăng nhập thất bại',
    locked_until DATETIME COMMENT 'Thời gian khóa tài khoản',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Ngày tạo',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Ngày cập nhật'
) ENGINE=InnoDB;

-- BẢNG ROLES
CREATE TABLE roles (
    role_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID vai trò',
    role_name VARCHAR(50) NOT NULL UNIQUE COMMENT 'Tên vai trò: Student, Teacher, Admin...',
    description TEXT COMMENT 'Mô tả quyền hạn của vai trò',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Vai trò có đang hoạt động không',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- BẢNG USER_ROLES
CREATE TABLE user_roles (
    user_id VARCHAR(20),
    role_id VARCHAR(20),
    assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời điểm gán vai trò',
    assigned_by VARCHAR(20) COMMENT 'Người gán vai trò',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Vai trò có đang hoạt động không',
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB;

-- BẢNG PERMISSIONS
CREATE TABLE permissions (
    permission_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID quyền',
    permission_name VARCHAR(100) NOT NULL UNIQUE COMMENT 'Tên quyền: /user/create, /user/read...',
    description TEXT COMMENT 'Mô tả hành động',
    resource VARCHAR(50) COMMENT 'Tài nguyên: user, role, topic, word...',
    action VARCHAR(20) COMMENT 'Hành động: create, read, update, delete',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Quyền có đang hoạt động không',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- BẢNG ROLE_PERMISSIONS
CREATE TABLE role_permissions (
    role_id VARCHAR(20),
    permission_id VARCHAR(20),
    granted_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời điểm cấp quyền',
    granted_by VARCHAR(20) COMMENT 'Người cấp quyền',
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB;

-- BẢNG OTP_CODES
CREATE TABLE otp_codes (
    otp_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID OTP',
    user_id VARCHAR(20) COMMENT 'Người dùng',
    email VARCHAR(100) COMMENT 'Email gửi OTP',
    phone_number VARCHAR(20) COMMENT 'Số điện thoại gửi OTP',
    otp_code VARCHAR(6) NOT NULL COMMENT 'Mã OTP 6 số',
    otp_type ENUM('email_verification', 'password_reset', 'login_2fa', 'phone_verification') NOT NULL COMMENT 'Loại OTP',
    is_used BOOLEAN DEFAULT FALSE COMMENT 'Đã sử dụng chưa',
    attempts INT DEFAULT 0 COMMENT 'Số lần thử nhập OTP',
    expires_at DATETIME NOT NULL COMMENT 'Thời gian hết hạn',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB;

-- BẢNG REFRESH_TOKENS
CREATE TABLE refresh_tokens (
    token_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID token',
    user_id VARCHAR(20) NOT NULL COMMENT 'Người dùng',
    token_hash VARCHAR(255) NOT NULL COMMENT 'Hash của refresh token',
    device_info TEXT COMMENT 'Thông tin thiết bị',
    ip_address VARCHAR(45) COMMENT 'Địa chỉ IP',
    user_agent TEXT COMMENT 'User agent',
    is_revoked BOOLEAN DEFAULT FALSE COMMENT 'Đã thu hồi chưa',
    expires_at DATETIME NOT NULL COMMENT 'Thời gian hết hạn',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB;

-- BẢNG EMAIL_VERIFICATIONS
CREATE TABLE email_verifications (
    verification_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID xác thực',
    user_id VARCHAR(20) NOT NULL COMMENT 'Người dùng',
    email VARCHAR(100) NOT NULL COMMENT 'Email cần xác thực',
    verification_token VARCHAR(255) NOT NULL COMMENT 'Token xác thực',
    is_verified BOOLEAN DEFAULT FALSE COMMENT 'Đã xác thực chưa',
    verified_at DATETIME COMMENT 'Thời gian xác thực',
    expires_at DATETIME NOT NULL COMMENT 'Thời gian hết hạn',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB;

-- BẢNG LOGIN_HISTORY
CREATE TABLE login_history (
    login_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID lịch sử',
    user_id VARCHAR(20) NOT NULL COMMENT 'Người dùng',
    login_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời gian đăng nhập',
    ip_address VARCHAR(45) COMMENT 'Địa chỉ IP',
    user_agent TEXT COMMENT 'User agent',
    login_status ENUM('success', 'failed', 'blocked', '2fa_required') NOT NULL COMMENT 'Trạng thái đăng nhập',
    failure_reason VARCHAR(255) COMMENT 'Lý do thất bại',
    session_duration INT COMMENT 'Thời gian session (giây)',
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB;

-- BẢNG PASSWORD_RESET_TOKENS
CREATE TABLE password_reset_tokens (
    reset_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID reset',
    user_id VARCHAR(20) NOT NULL COMMENT 'Người dùng',
    reset_token VARCHAR(255) NOT NULL COMMENT 'Token reset password',
    is_used BOOLEAN DEFAULT FALSE COMMENT 'Đã sử dụng chưa',
    expires_at DATETIME NOT NULL COMMENT 'Thời gian hết hạn',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB;

-- BẢNG TOPICS
CREATE TABLE topics (
    topic_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID chủ đề',
    topic_name VARCHAR(255) NOT NULL COMMENT 'Tên chủ đề từ vựng',
    description TEXT COMMENT 'Mô tả nội dung chủ đề',
    image_url VARCHAR(255) COMMENT 'Ảnh đại diện cho chủ đề',
    logo_url VARCHAR(255) COMMENT 'Logo website (cho topic hệ thống)',
    topic_type ENUM('system', 'user_created') NOT NULL COMMENT 'Loại topic: hệ thống hoặc user tạo',
    created_by VARCHAR(20) COMMENT 'ID người tạo, NULL nếu là topic hệ thống',
    is_public BOOLEAN DEFAULT TRUE COMMENT 'Topic có công khai không (cho user topic)',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Topic có đang hoạt động không',
    word_count INT DEFAULT 0 COMMENT 'Số lượng từ trong topic',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Ngày tạo chủ đề',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Ngày cập nhật',
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB;

-- BẢNG WORDS
CREATE TABLE words (
    word_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID từ vựng',
    topic_id VARCHAR(20) NOT NULL COMMENT 'Thuộc chủ đề nào',
    word VARCHAR(255) NOT NULL COMMENT 'Từ tiếng Anh',
    part_of_speech VARCHAR(50) COMMENT 'Loại từ: noun, verb, adjective...',
    pronunciation VARCHAR(255) COMMENT 'Phiên âm (IPA)',
    meaning_vi TEXT NOT NULL COMMENT 'Nghĩa tiếng Việt',
    example_en TEXT COMMENT 'Câu ví dụ tiếng Anh',
    example_vi TEXT COMMENT 'Dịch câu ví dụ',
    image_url VARCHAR(255) COMMENT 'Ảnh minh họa cho từ',
    notes TEXT COMMENT 'Ghi chú cho từ',
    word_type ENUM('system', 'user_created') NOT NULL COMMENT 'Loại từ: hệ thống hoặc user tạo',
    created_by VARCHAR(20) COMMENT 'ID người tạo, NULL nếu là từ hệ thống',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Từ có đang hoạt động không',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Ngày tạo từ',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Ngày cập nhật',
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB;

-- BẢNG USER_WORDS
CREATE TABLE user_words (
    user_word_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID từ vựng cá nhân',
    user_id VARCHAR(20) NOT NULL COMMENT 'Người sở hữu từ',
    topic_id VARCHAR(20) NOT NULL COMMENT 'Thuộc chủ đề nào',
    word VARCHAR(255) NOT NULL COMMENT 'Từ tiếng Anh',
    part_of_speech VARCHAR(50) COMMENT 'Loại từ',
    pronunciation VARCHAR(255) COMMENT 'Phiên âm',
    meaning_vi TEXT NOT NULL COMMENT 'Nghĩa tiếng Việt',
    example_en TEXT COMMENT 'Câu ví dụ tiếng Anh',
    example_vi TEXT COMMENT 'Dịch câu ví dụ',
    image_url VARCHAR(255) COMMENT 'Ảnh minh họa',
    notes TEXT COMMENT 'Ghi chú cá nhân',
    from_system_word_id VARCHAR(20) COMMENT 'Nếu copy từ hệ thống thì lưu ID gốc',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Từ có đang hoạt động không',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Ngày tạo từ',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Ngày cập nhật',
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id) ON DELETE CASCADE,
    FOREIGN KEY (from_system_word_id) REFERENCES words(word_id) ON DELETE SET NULL
) ENGINE=InnoDB;

-- BẢNG WORD_LEARNING_PROGRESS
CREATE TABLE word_learning_progress (
    progress_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID tiến độ',
    user_id VARCHAR(20) NOT NULL COMMENT 'Người học',
    word_id VARCHAR(20) COMMENT 'ID từ hệ thống (nếu học từ hệ thống)',
    user_word_id VARCHAR(20) COMMENT 'ID từ cá nhân (nếu học từ cá nhân)',
    topic_id VARCHAR(20) NOT NULL COMMENT 'Chủ đề đang học',
    learning_status ENUM('not_started', 'learning', 'mastered', 'review') DEFAULT 'not_started' COMMENT 'Trạng thái học',
    mastery_level INT DEFAULT 0 COMMENT 'Mức độ thuộc từ (0-5)',
    is_marked_as_learned BOOLEAN DEFAULT FALSE COMMENT 'Đã đánh dấu học xong chưa (dấu sao)',
    marked_at DATETIME COMMENT 'Thời gian đánh dấu học xong',
    last_reviewed_at DATETIME COMMENT 'Lần ôn tập cuối',
    review_count INT DEFAULT 0 COMMENT 'Số lần ôn tập',
    correct_answers INT DEFAULT 0 COMMENT 'Số câu trả lời đúng',
    total_attempts INT DEFAULT 0 COMMENT 'Tổng số lần thử',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Ngày bắt đầu học',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Ngày cập nhật',
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (word_id) REFERENCES words(word_id) ON DELETE CASCADE,
    FOREIGN KEY (user_word_id) REFERENCES user_words(user_word_id) ON DELETE CASCADE,
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id) ON DELETE CASCADE
) ENGINE=InnoDB;

-- BẢNG FAVORITE_TOPICS
CREATE TABLE favorite_topics (
    favorite_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID yêu thích',
    user_id VARCHAR(20) NOT NULL COMMENT 'Người dùng',
    topic_id VARCHAR(20) NOT NULL COMMENT 'Chủ đề yêu thích',
    added_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Ngày thêm vào yêu thích',
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_topic (user_id, topic_id)
) ENGINE=InnoDB;

-- BẢNG STUDY_SESSIONS
CREATE TABLE study_sessions (
    session_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID phiên học',
    user_id VARCHAR(20) NOT NULL COMMENT 'Người học',
    topic_id VARCHAR(20) NOT NULL COMMENT 'Chủ đề đang học',
    session_type ENUM('flashcard', 'list_view', 'quiz', 'review') NOT NULL COMMENT 'Loại phiên học',
    started_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời gian bắt đầu',
    ended_at DATETIME COMMENT 'Thời gian kết thúc',
    total_words INT DEFAULT 0 COMMENT 'Tổng số từ trong phiên',
    learned_words INT DEFAULT 0 COMMENT 'Số từ đã học xong',
    session_duration INT COMMENT 'Thời gian phiên học (giây)',
    is_completed BOOLEAN DEFAULT FALSE COMMENT 'Phiên học đã hoàn thành chưa',
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id) ON DELETE CASCADE
) ENGINE=InnoDB;

-- BẢNG SESSION_DETAILS
CREATE TABLE session_details (
    detail_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID chi tiết',
    session_id VARCHAR(20) NOT NULL COMMENT 'Thuộc phiên học nào',
    word_id VARCHAR(20) COMMENT 'ID từ hệ thống',
    user_word_id VARCHAR(20) COMMENT 'ID từ cá nhân',
    action_type ENUM('view', 'flip', 'mark_learned', 'unmark_learned', 'skip', 'correct', 'incorrect') NOT NULL COMMENT 'Hành động thực hiện',
    action_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời gian thực hiện',
    time_spent INT COMMENT 'Thời gian xem từ (giây)',
    is_correct BOOLEAN COMMENT 'Trả lời đúng hay sai (cho quiz)',
    notes TEXT COMMENT 'Ghi chú cho hành động',
    FOREIGN KEY (session_id) REFERENCES study_sessions(session_id) ON DELETE CASCADE,
    FOREIGN KEY (word_id) REFERENCES words(word_id) ON DELETE CASCADE,
    FOREIGN KEY (user_word_id) REFERENCES user_words(user_word_id) ON DELETE CASCADE
) ENGINE=InnoDB;

-- BẢNG WORD_STUDY_HISTORY
CREATE TABLE word_study_history (
    history_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID lịch sử',
    user_id VARCHAR(20) NOT NULL COMMENT 'Người học',
    word_id VARCHAR(20) COMMENT 'ID từ hệ thống',
    user_word_id VARCHAR(20) COMMENT 'ID từ cá nhân',
    topic_id VARCHAR(20) NOT NULL COMMENT 'Chủ đề học',
    action_type ENUM('view', 'mark_learned', 'unmark_learned', 'review', 'start_learning') NOT NULL COMMENT 'Hành động',
    action_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời gian thực hiện',
    session_id VARCHAR(20) COMMENT 'Thuộc phiên học nào',
    mastery_level_before INT COMMENT 'Mức độ thuộc trước khi thực hiện',
    mastery_level_after INT COMMENT 'Mức độ thuộc sau khi thực hiện',
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (word_id) REFERENCES words(word_id) ON DELETE CASCADE,
    FOREIGN KEY (user_word_id) REFERENCES user_words(user_word_id) ON DELETE CASCADE,
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES study_sessions(session_id) ON DELETE SET NULL
) ENGINE=InnoDB;

-- BẢNG LEARNING_STATISTICS
CREATE TABLE learning_statistics (
    stat_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID thống kê',
    user_id VARCHAR(20) NOT NULL COMMENT 'Người học',
    topic_id VARCHAR(20) NOT NULL COMMENT 'Chủ đề',
    date DATE NOT NULL COMMENT 'Ngày thống kê',
    total_words INT DEFAULT 0 COMMENT 'Tổng số từ trong topic',
    learned_words INT DEFAULT 0 COMMENT 'Số từ đã học xong',
    study_time INT DEFAULT 0 COMMENT 'Thời gian học (phút)',
    sessions_count INT DEFAULT 0 COMMENT 'Số phiên học',
    accuracy_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Tỷ lệ chính xác (%)',
    words_marked_learned INT DEFAULT 0 COMMENT 'Số từ đánh dấu học xong trong ngày',
    words_unmarked INT DEFAULT 0 COMMENT 'Số từ bỏ đánh dấu trong ngày',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Ngày tạo thống kê',
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_topic_date (user_id, topic_id, date)
) ENGINE=InnoDB;

-- BẢNG BATCH_IMPORTS
CREATE TABLE batch_imports (
    import_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID import',
    user_id VARCHAR(20) NOT NULL COMMENT 'Người import',
    topic_id VARCHAR(20) NOT NULL COMMENT 'Chủ đề import vào',
    import_name VARCHAR(255) COMMENT 'Tên file import',
    total_words INT DEFAULT 0 COMMENT 'Tổng số từ import',
    success_count INT DEFAULT 0 COMMENT 'Số từ import thành công',
    error_count INT DEFAULT 0 COMMENT 'Số từ lỗi',
    import_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending' COMMENT 'Trạng thái import',
    error_log TEXT COMMENT 'Log lỗi nếu có',
    started_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời gian bắt đầu',
    completed_at DATETIME COMMENT 'Thời gian hoàn thành',
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id) ON DELETE CASCADE
) ENGINE=InnoDB;

-- BẢNG IMPORT_DETAILS (Sửa lỗi row_number)
CREATE TABLE import_details (
    detail_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID chi tiết',
    import_id VARCHAR(20) NOT NULL COMMENT 'Thuộc import nào',
    row_num INT COMMENT 'Số thứ tự dòng trong file',
    word VARCHAR(255) COMMENT 'Từ tiếng Anh',
    meaning_vi TEXT COMMENT 'Nghĩa tiếng Việt',
    part_of_speech VARCHAR(50) COMMENT 'Loại từ',
    pronunciation VARCHAR(255) COMMENT 'Phiên âm',
    example_en TEXT COMMENT 'Ví dụ tiếng Anh',
    example_vi TEXT COMMENT 'Ví dụ tiếng Việt',
    notes TEXT COMMENT 'Ghi chú',
    import_status ENUM('pending', 'success', 'error') DEFAULT 'pending' COMMENT 'Trạng thái import từng từ',
    error_message TEXT COMMENT 'Thông báo lỗi nếu có',
    created_word_id VARCHAR(20) COMMENT 'ID từ được tạo thành công',
    FOREIGN KEY (import_id) REFERENCES batch_imports(import_id) ON DELETE CASCADE,
    FOREIGN KEY (created_word_id) REFERENCES user_words(user_word_id) ON DELETE SET NULL
) ENGINE=InnoDB;

-- BẢNG STUDY_MODES
CREATE TABLE study_modes (
    mode_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID chế độ học',
    mode_name VARCHAR(50) NOT NULL UNIQUE COMMENT 'Tên chế độ: flashcard, list_view, review_only',
    description TEXT COMMENT 'Mô tả chế độ học',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Chế độ có đang hoạt động không',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- BẢNG STUDY_SETTINGS
CREATE TABLE study_settings (
    setting_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID cài đặt',
    user_id VARCHAR(20) NOT NULL COMMENT 'Người dùng',
    topic_id VARCHAR(20) NOT NULL COMMENT 'Chủ đề',
    auto_mark_learned BOOLEAN DEFAULT FALSE COMMENT 'Tự động đánh dấu học xong sau N lần đúng',
    auto_mark_threshold INT DEFAULT 3 COMMENT 'Số lần đúng để tự động đánh dấu học xong',
    show_learned_words BOOLEAN DEFAULT FALSE COMMENT 'Hiển thị từ đã học trong danh sách',
    review_interval INT DEFAULT 7 COMMENT 'Khoảng thời gian ôn tập (ngày)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Ngày tạo cài đặt',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Ngày cập nhật',
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_topic_setting (user_id, topic_id)
) ENGINE=InnoDB;

-- BẢNG STUDY_NOTIFICATIONS
CREATE TABLE study_notifications (
    notification_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID thông báo',
    user_id VARCHAR(20) NOT NULL COMMENT 'Người nhận',
    topic_id VARCHAR(20) COMMENT 'Chủ đề liên quan',
    notification_type ENUM('review_reminder', 'achievement', 'streak_reminder', 'new_words') NOT NULL COMMENT 'Loại thông báo',
    title VARCHAR(255) NOT NULL COMMENT 'Tiêu đề thông báo',
    message TEXT NOT NULL COMMENT 'Nội dung thông báo',
    is_read BOOLEAN DEFAULT FALSE COMMENT 'Đã đọc chưa',
    read_at DATETIME COMMENT 'Thời gian đọc',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời gian tạo',
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id) ON DELETE SET NULL
) ENGINE=InnoDB;

-- BẢNG LEARNING_ACHIEVEMENTS
CREATE TABLE learning_achievements (
    achievement_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID thành tích',
    user_id VARCHAR(20) NOT NULL COMMENT 'Người dùng',
    topic_id VARCHAR(20) COMMENT 'Chủ đề',
    achievement_type ENUM('first_word', 'word_streak', 'topic_completed', 'perfect_score', 'study_time') NOT NULL COMMENT 'Loại thành tích',
    achievement_name VARCHAR(255) NOT NULL COMMENT 'Tên thành tích',
    description TEXT COMMENT 'Mô tả thành tích',
    value INT COMMENT 'Giá trị đạt được (số từ, thời gian, điểm...)',
    earned_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời gian đạt được',
    is_notified BOOLEAN DEFAULT FALSE COMMENT 'Đã thông báo chưa',
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id) ON DELETE SET NULL
) ENGINE=InnoDB;

-- =============================================
-- INSERT DỮ LIỆU MẪU
-- =============================================

-- INSERT USERS
INSERT INTO users (user_id, username, password_hash, email, full_name, phone_number, status, email_verified) VALUES
('USER_001', 'admin1', '$2b$10$O9h2iY2w4D7d7m7sWw1f6u9KqvWwX6mVY0zRz1n0c7r9b6v0cCw9S', '<EMAIL>', 'System Administrator', '0900000001', 'active', TRUE),
('USER_002', 'teacher1', '$2b$10$O9h2iY2w4D7d7m7sWw1f6u9KqvWwX6mVY0zRz1n0c7r9b6v0cCw9S', '<EMAIL>', 'John Teacher', '0900000002', 'active', TRUE),
('USER_003', 'student1', '$2b$10$O9h2iY2w4D7d7m7sWw1f6u9KqvWwX6mVY0zRz1n0c7r9b6v0cCw9S', '<EMAIL>', 'Alice Student', '0900000003', 'active', TRUE),
('USER_004', 'student2', '$2b$10$O9h2iY2w4D7d7m7sWw1f6u9KqvWwX6mVY0zRz1n0c7r9b6v0cCw9S', '<EMAIL>', 'Bob Learner', '0900000004', 'active', TRUE),
('USER_005', 'guest1', '$2b$10$O9h2iY2w4D7d7m7sWw1f6u9KqvWwX6mVY0zRz1n0c7r9b6v0cCw9S', '<EMAIL>', 'Guest Viewer', '0900000005', 'active', TRUE);

-- INSERT ROLES
INSERT INTO roles (role_id, role_name, description) VALUES
('ROLE_001', 'Admin', 'Toàn quyền hệ thống'),
('ROLE_002', 'Teacher', 'Quản lý chủ đề, từ vựng, bài tập'),
('ROLE_003', 'Student', 'Học và làm bài tập'),
('ROLE_004', 'Guest', 'Chỉ đọc nội dung công khai');

-- INSERT PERMISSIONS
INSERT INTO permissions (permission_id, permission_name, description, resource, action) VALUES
-- User management
('PERM_001', '/user/create', 'Tạo người dùng', 'user', 'create'),
('PERM_002', '/user/read', 'Xem danh sách / chi tiết người dùng', 'user', 'read'),
('PERM_003', '/user/update', 'Cập nhật người dùng', 'user', 'update'),
('PERM_004', '/user/delete', 'Xóa người dùng', 'user', 'delete'),
-- Role management
('PERM_005', '/role/create', 'Tạo vai trò', 'role', 'create'),
('PERM_006', '/role/read', 'Xem vai trò', 'role', 'read'),
('PERM_007', '/role/update', 'Cập nhật vai trò', 'role', 'update'),
('PERM_008', '/role/delete', 'Xóa vai trò', 'role', 'delete'),
('PERM_009', '/role/assign', 'Gán vai trò cho người dùng', 'role', 'assign'),
-- Permission management
('PERM_010', '/permission/create', 'Tạo quyền', 'permission', 'create'),
('PERM_011', '/permission/read', 'Xem quyền', 'permission', 'read'),
('PERM_012', '/permission/update', 'Cập nhật quyền', 'permission', 'update'),
('PERM_013', '/permission/delete', 'Xóa quyền', 'permission', 'delete'),
('PERM_014', '/permission/assign', 'Gán quyền cho vai trò', 'permission', 'assign'),
-- Topic management
('PERM_015', '/topic/create', 'Tạo chủ đề', 'topic', 'create'),
('PERM_016', '/topic/read', 'Xem chủ đề', 'topic', 'read'),
('PERM_017', '/topic/update', 'Cập nhật chủ đề', 'topic', 'update'),
('PERM_018', '/topic/delete', 'Xóa chủ đề', 'topic', 'delete'),
-- Word management
('PERM_019', '/word/create', 'Tạo từ vựng', 'word', 'create'),
('PERM_020', '/word/read', 'Xem từ vựng', 'word', 'read'),
('PERM_021', '/word/update', 'Cập nhật từ vựng', 'word', 'update'),
('PERM_022', '/word/delete', 'Xóa từ vựng', 'word', 'delete'),
-- Flashcard learning
('PERM_023', '/flashcard/start', 'Bắt đầu học flashcard', 'flashcard', 'start'),
('PERM_024', '/flashcard/review', 'Ôn tập flashcard', 'flashcard', 'review'),
('PERM_025', '/flashcard/mark', 'Đánh dấu đã thuộc / loại khỏi ôn tập', 'flashcard', 'mark'),
-- Exercise
('PERM_026', '/exercise/create', 'Tạo bài tập', 'exercise', 'create'),
('PERM_027', '/exercise/read', 'Xem bài tập', 'exercise', 'read'),
('PERM_028', '/exercise/update', 'Cập nhật bài tập', 'exercise', 'update'),
('PERM_029', '/exercise/delete', 'Xóa bài tập', 'exercise', 'delete'),
('PERM_030', '/exercise/submit', 'Nộp bài làm', 'exercise', 'submit');

-- INSERT USER_ROLES
INSERT INTO user_roles (user_id, role_id, assigned_by) VALUES
('USER_001', 'ROLE_001', 'USER_001'),
('USER_002', 'ROLE_002', 'USER_001'),
('USER_003', 'ROLE_003', 'USER_001'),
('USER_004', 'ROLE_003', 'USER_001'),
('USER_005', 'ROLE_004', 'USER_001');

-- INSERT ROLE_PERMISSIONS
-- Admin: full permissions
INSERT INTO role_permissions (role_id, permission_id, granted_by)
SELECT 'ROLE_001' AS role_id, permission_id, 'USER_001' AS granted_by FROM permissions;

-- Teacher: topic/word CRUD, exercise CRUD, xem exercise
INSERT INTO role_permissions (role_id, permission_id, granted_by) VALUES
('ROLE_002', 'PERM_015', 'USER_001'),('ROLE_002', 'PERM_016', 'USER_001'),('ROLE_002', 'PERM_017', 'USER_001'),('ROLE_002', 'PERM_018', 'USER_001'),
('ROLE_002', 'PERM_019', 'USER_001'),('ROLE_002', 'PERM_020', 'USER_001'),('ROLE_002', 'PERM_021', 'USER_001'),('ROLE_002', 'PERM_022', 'USER_001'),
('ROLE_002', 'PERM_026', 'USER_001'),('ROLE_002', 'PERM_027', 'USER_001'),('ROLE_002', 'PERM_028', 'USER_001'),('ROLE_002', 'PERM_029', 'USER_001');

-- Student: đọc nội dung + học flashcard + nộp bài
INSERT INTO role_permissions (role_id, permission_id, granted_by) VALUES
('ROLE_003', 'PERM_016', 'USER_001'),('ROLE_003', 'PERM_020', 'USER_001'),
('ROLE_003', 'PERM_023', 'USER_001'),('ROLE_003', 'PERM_024', 'USER_001'),('ROLE_003', 'PERM_025', 'USER_001'),
('ROLE_003', 'PERM_027', 'USER_001'),('ROLE_003', 'PERM_030', 'USER_001');

-- Guest: chỉ read topic/word
INSERT INTO role_permissions (role_id, permission_id, granted_by) VALUES
('ROLE_004', 'PERM_016', 'USER_001'),('ROLE_004', 'PERM_020', 'USER_001');

-- INSERT TOPICS
INSERT INTO topics (topic_id, topic_name, description, image_url, topic_type, created_by, word_count) VALUES
('TOPIC_001', 'Từ vựng tiếng Anh văn phòng', 'Từ vựng dùng trong môi trường công sở', 'https://img.example.com/office.jpg', 'system', 'USER_002', 6),
('TOPIC_002', 'Tiếng Anh giao tiếp cơ bản', 'Câu và từ vựng giao tiếp hằng ngày', 'https://img.example.com/basic-comm.jpg', 'system', 'USER_002', 6),
('TOPIC_003', '900 từ TOEFL (mẫu)', 'Từ vựng thường gặp trong TOEFL', 'https://img.example.com/toefl.jpg', 'system', 'USER_001', 6),
('TOPIC_004', '900 từ IELTS (mẫu)', 'Từ vựng thường gặp trong IELTS', 'https://img.example.com/ielts.jpg', 'system', 'USER_002', 6),
('TOPIC_005', 'My Business Words', 'Từ vựng kinh doanh cá nhân', 'https://img.example.com/business.jpg', 'user_created', 'USER_003', 3),
('TOPIC_006', 'Travel Vocabulary', 'Từ vựng du lịch cá nhân', 'https://img.example.com/travel.jpg', 'user_created', 'USER_004', 2);

-- INSERT WORDS (System words)
INSERT INTO words (word_id, topic_id, word, part_of_speech, pronunciation, meaning_vi, example_en, example_vi, word_type, created_by) VALUES
-- Topic 1: Office
('WORD_001', 'TOPIC_001', 'absent', 'adjective', '/ˈæbsənt/', 'vắng mặt', 'Most students were absent from school at least once.', 'Hầu hết sinh viên đã vắng mặt ít nhất một lần.', 'system', 'USER_002'),
('WORD_002', 'TOPIC_001', 'approve', 'verb', '/əˈpruːv/', 'chấp thuận', 'The manager approved the budget for Q3.', 'Quản lý đã chấp thuận ngân sách quý 3.', 'system', 'USER_002'),
('WORD_003', 'TOPIC_001', 'deadline', 'noun', '/ˈdedlaɪn/', 'hạn chót', 'We must meet the project deadline.', 'Chúng ta phải kịp hạn chót dự án.', 'system', 'USER_002'),
('WORD_004', 'TOPIC_001', 'meeting', 'noun', '/ˈmiːtɪŋ/', 'cuộc họp', 'The weekly meeting is on Monday morning.', 'Cuộc họp hằng tuần vào sáng thứ Hai.', 'system', 'USER_002'),
('WORD_005', 'TOPIC_001', 'bonus', 'noun', '/ˈbəʊnəs/', 'tiền thưởng', 'Employees receive a bonus for high performance.', 'Nhân viên nhận thưởng khi hiệu suất cao.', 'system', 'USER_002'),
('WORD_006', 'TOPIC_001', 'resign', 'verb', '/rɪˈzaɪn/', 'từ chức', 'He decided to resign from his position.', 'Anh ấy quyết định từ chức.', 'system', 'USER_002'),

-- Topic 2: Basic Communication
('WORD_007', 'TOPIC_002', 'greet', 'verb', '/ɡriːt/', 'chào hỏi', 'They greeted each other warmly.', 'Họ chào nhau nồng nhiệt.', 'system', 'USER_002'),
('WORD_008', 'TOPIC_002', 'introduce', 'verb', '/ˌɪntrəˈdjuːs/', 'giới thiệu', 'Let me introduce you to my friend.', 'Để tôi giới thiệu bạn với bạn tôi.', 'system', 'USER_002'),
('WORD_009', 'TOPIC_002', 'polite', 'adjective', '/pəˈlaɪt/', 'lịch sự', 'It is polite to say thank you.', 'Lịch sự khi nói cảm ơn.', 'system', 'USER_002'),
('WORD_010', 'TOPIC_002', 'request', 'noun', '/rɪˈkwest/', 'yêu cầu', 'I sent a request for information.', 'Tôi đã gửi yêu cầu thông tin.', 'system', 'USER_002'),
('WORD_011', 'TOPIC_002', 'respond', 'verb', '/rɪˈspɒnd/', 'phản hồi', 'Please respond to my email.', 'Vui lòng phản hồi email của tôi.', 'system', 'USER_002'),
('WORD_012', 'TOPIC_002', 'confirm', 'verb', '/kənˈfɜːm/', 'xác nhận', 'She confirmed the reservation.', 'Cô ấy xác nhận đặt chỗ.', 'system', 'USER_002'),

-- Topic 3: TOEFL
('WORD_013', 'TOPIC_003', 'analyze', 'verb', '/ˈænəlaɪz/', 'phân tích', 'Analyze the data carefully.', 'Phân tích dữ liệu cẩn thận.', 'system', 'USER_001'),
('WORD_014', 'TOPIC_003', 'assume', 'verb', '/əˈsjuːm/', 'giả định', 'Do not assume the results.', 'Đừng giả định kết quả.', 'system', 'USER_001'),
('WORD_015', 'TOPIC_003', 'distinct', 'adjective', '/dɪˈstɪŋkt/', 'riêng biệt', 'Two distinct ideas.', 'Hai ý tưởng riêng biệt.', 'system', 'USER_001'),
('WORD_016', 'TOPIC_003', 'estimate', 'verb', '/ˈestɪmeɪt/', 'ước tính', 'We estimate the cost.', 'Chúng tôi ước tính chi phí.', 'system', 'USER_001'),
('WORD_017', 'TOPIC_003', 'interpret', 'verb', '/ɪnˈtɜːprɪt/', 'diễn giải', 'Interpret the chart.', 'Diễn giải biểu đồ.', 'system', 'USER_001'),
-- Topic 3: TOEFL (tiếp)
('WORD_018', 'TOPIC_003', 'predict', 'verb', '/prɪˈdɪkt/', 'dự đoán', 'Predict the trend.', 'Dự đoán xu hướng.', 'system', 'USER_001'),

-- Topic 4: IELTS
('WORD_019', 'TOPIC_004', 'coherent', 'adjective', '/kəʊˈhɪərənt/', 'mạch lạc', 'A coherent essay.', 'Bài luận mạch lạc.', 'system', 'USER_002'),
('WORD_020', 'TOPIC_004', 'derive', 'verb', '/dɪˈraɪv/', 'bắt nguồn', 'This word derives from Latin.', 'Từ này bắt nguồn từ tiếng Latin.', 'system', 'USER_002'),
('WORD_021', 'TOPIC_004', 'fluctuate', 'verb', '/ˈflʌktʃueɪt/', 'dao động', 'Prices fluctuate daily.', 'Giá dao động hằng ngày.', 'system', 'USER_002'),
('WORD_022', 'TOPIC_004', 'perspective', 'noun', '/pəˈspektɪv/', 'quan điểm', 'From my perspective...', 'Từ quan điểm của tôi...', 'system', 'USER_002'),
('WORD_023', 'TOPIC_004', 'subsequent', 'adjective', '/ˈsʌbsɪkwənt/', 'tiếp theo', 'Subsequent changes occurred.', 'Những thay đổi tiếp theo đã xảy ra.', 'system', 'USER_002'),
('WORD_024', 'TOPIC_004', 'viable', 'adjective', '/ˈvaɪəbl/', 'khả thi', 'A viable solution.', 'Giải pháp khả thi.', 'system', 'USER_002');

-- INSERT USER_WORDS (Personal words)
INSERT INTO user_words (user_word_id, user_id, topic_id, word, part_of_speech, pronunciation, meaning_vi, example_en, example_vi, notes) VALUES
-- User 3's business words
('UWORD_001', 'USER_003', 'TOPIC_005', 'revenue', 'noun', '/ˈrevənjuː/', 'doanh thu', 'The company revenue increased by 20%.', 'Doanh thu công ty tăng 20%.', 'Từ kinh doanh quan trọng'),
('UWORD_002', 'USER_003', 'TOPIC_005', 'profit', 'noun', '/ˈprɒfɪt/', 'lợi nhuận', 'We made a good profit this quarter.', 'Chúng ta có lợi nhuận tốt quý này.', 'Mục tiêu kinh doanh'),
('UWORD_003', 'USER_003', 'TOPIC_005', 'strategy', 'noun', '/ˈstrætədʒi/', 'chiến lược', 'Our marketing strategy is working well.', 'Chiến lược marketing của chúng ta đang hoạt động tốt.', 'Kế hoạch dài hạn'),

-- User 4's travel words
('UWORD_004', 'USER_004', 'TOPIC_006', 'itinerary', 'noun', '/aɪˈtɪnərəri/', 'lịch trình', 'Check the itinerary before departure.', 'Kiểm tra lịch trình trước khi khởi hành.', 'Kế hoạch du lịch'),
('UWORD_005', 'USER_004', 'TOPIC_006', 'souvenir', 'noun', '/ˌsuːvəˈnɪə/', 'quà lưu niệm', 'Buy a souvenir for your family.', 'Mua quà lưu niệm cho gia đình.', 'Kỷ niệm chuyến đi');

-- INSERT FAVORITE_TOPICS
INSERT INTO favorite_topics (favorite_id, user_id, topic_id) VALUES
('FAV_001', 'USER_003', 'TOPIC_001'),
('FAV_002', 'USER_003', 'TOPIC_002'),
('FAV_003', 'USER_003', 'TOPIC_003'),
('FAV_004', 'USER_004', 'TOPIC_004'),
('FAV_005', 'USER_004', 'TOPIC_001');

-- INSERT WORD_LEARNING_PROGRESS
INSERT INTO word_learning_progress (progress_id, user_id, word_id, topic_id, learning_status, mastery_level, is_marked_as_learned, marked_at, review_count, correct_answers, total_attempts) VALUES
-- User 3's progress on system words
('PROG_001', 'USER_003', 'WORD_001', 'TOPIC_001', 'mastered', 5, TRUE, NOW(), 3, 5, 5),
('PROG_002', 'USER_003', 'WORD_002', 'TOPIC_001', 'learning', 3, FALSE, NULL, 2, 3, 5),
('PROG_003', 'USER_003', 'WORD_003', 'TOPIC_001', 'mastered', 5, TRUE, NOW(), 4, 6, 6),
('PROG_004', 'USER_003', 'WORD_007', 'TOPIC_002', 'learning', 2, FALSE, NULL, 1, 2, 3),
('PROG_005', 'USER_003', 'WORD_008', 'TOPIC_002', 'not_started', 0, FALSE, NULL, 0, 0, 0),

-- User 4's progress on system words
('PROG_006', 'USER_004', 'WORD_019', 'TOPIC_004', 'mastered', 5, TRUE, NOW(), 3, 4, 4),
('PROG_007', 'USER_004', 'WORD_020', 'TOPIC_004', 'learning', 3, FALSE, NULL, 2, 3, 4),
('PROG_008', 'USER_004', 'WORD_021', 'TOPIC_004', 'not_started', 0, FALSE, NULL, 0, 0, 0);

-- INSERT WORD_LEARNING_PROGRESS for user words
INSERT INTO word_learning_progress (progress_id, user_id, user_word_id, topic_id, learning_status, mastery_level, is_marked_as_learned, marked_at, review_count, correct_answers, total_attempts) VALUES
-- User 3's progress on personal words
('PROG_009', 'USER_003', 'UWORD_001', 'TOPIC_005', 'mastered', 5, TRUE, NOW(), 2, 3, 3),
('PROG_010', 'USER_003', 'UWORD_002', 'TOPIC_005', 'learning', 2, FALSE, NULL, 1, 1, 2),
('PROG_011', 'USER_003', 'UWORD_003', 'TOPIC_005', 'not_started', 0, FALSE, NULL, 0, 0, 0),

-- User 4's progress on personal words
('PROG_012', 'USER_004', 'UWORD_004', 'TOPIC_006', 'mastered', 5, TRUE, NOW(), 2, 2, 2),
('PROG_013', 'USER_004', 'UWORD_005', 'TOPIC_006', 'learning', 1, FALSE, NULL, 1, 1, 2);

-- INSERT STUDY_SESSIONS
INSERT INTO study_sessions (session_id, user_id, topic_id, session_type, started_at, ended_at, total_words, learned_words, session_duration, is_completed) VALUES
('SESS_001', 'USER_003', 'TOPIC_001', 'flashcard', NOW() - INTERVAL 2 HOUR, NOW() - INTERVAL 1 HOUR, 6, 2, 3600, TRUE),
('SESS_002', 'USER_003', 'TOPIC_002', 'list_view', NOW() - INTERVAL 1 HOUR, NOW() - INTERVAL 30 MINUTE, 6, 0, 1800, TRUE),
('SESS_003', 'USER_004', 'TOPIC_004', 'flashcard', NOW() - INTERVAL 30 MINUTE, NOW() - INTERVAL 15 MINUTE, 6, 1, 900, TRUE),
('SESS_004', 'USER_003', 'TOPIC_005', 'flashcard', NOW() - INTERVAL 15 MINUTE, NULL, 3, 1, 900, FALSE);

-- INSERT SESSION_DETAILS
INSERT INTO session_details (detail_id, session_id, word_id, action_type, action_time, time_spent, is_correct) VALUES
-- Session 1 details
('DET_001', 'SESS_001', 'WORD_001', 'view', NOW() - INTERVAL 2 HOUR, 30, NULL),
('DET_002', 'SESS_001', 'WORD_001', 'flip', NOW() - INTERVAL 2 HOUR + INTERVAL 30 SECOND, 15, NULL),
('DET_003', 'SESS_001', 'WORD_001', 'mark_learned', NOW() - INTERVAL 2 HOUR + INTERVAL 45 SECOND, 0, NULL),
('DET_004', 'SESS_001', 'WORD_002', 'view', NOW() - INTERVAL 2 HOUR + INTERVAL 1 MINUTE, 25, NULL),
('DET_005', 'SESS_001', 'WORD_002', 'flip', NOW() - INTERVAL 2 HOUR + INTERVAL 1 MINUTE + INTERVAL 25 SECOND, 20, NULL),
('DET_006', 'SESS_001', 'WORD_002', 'skip', NOW() - INTERVAL 2 HOUR + INTERVAL 1 MINUTE + INTERVAL 45 SECOND, 0, NULL),

-- Session 2 details
('DET_007', 'SESS_002', 'WORD_007', 'view', NOW() - INTERVAL 1 HOUR, 45, NULL),
('DET_008', 'SESS_002', 'WORD_008', 'view', NOW() - INTERVAL 1 HOUR + INTERVAL 1 MINUTE, 30, NULL),

-- Session 3 details
('DET_009', 'SESS_003', 'WORD_019', 'view', NOW() - INTERVAL 30 MINUTE, 20, NULL),
('DET_010', 'SESS_003', 'WORD_019', 'flip', NOW() - INTERVAL 30 MINUTE + INTERVAL 20 SECOND, 15, NULL),
('DET_011', 'SESS_003', 'WORD_019', 'mark_learned', NOW() - INTERVAL 30 MINUTE + INTERVAL 35 SECOND, 0, NULL);

-- INSERT SESSION_DETAILS for user words
INSERT INTO session_details (detail_id, session_id, user_word_id, action_type, action_time, time_spent, is_correct) VALUES
-- Session 4 details (user words)
('DET_012', 'SESS_004', 'UWORD_001', 'view', NOW() - INTERVAL 15 MINUTE, 25, NULL),
('DET_013', 'SESS_004', 'UWORD_001', 'flip', NOW() - INTERVAL 15 MINUTE + INTERVAL 25 SECOND, 20, NULL),
('DET_014', 'SESS_004', 'UWORD_001', 'mark_learned', NOW() - INTERVAL 15 MINUTE + INTERVAL 45 SECOND, 0, NULL),
('DET_015', 'SESS_004', 'UWORD_002', 'view', NOW() - INTERVAL 15 MINUTE + INTERVAL 1 MINUTE, 30, NULL);

-- INSERT WORD_STUDY_HISTORY
INSERT INTO word_study_history (history_id, user_id, word_id, topic_id, action_type, action_time, session_id, mastery_level_before, mastery_level_after) VALUES
('HIST_001', 'USER_003', 'WORD_001', 'TOPIC_001', 'start_learning', NOW() - INTERVAL 2 HOUR, 'SESS_001', 0, 1),
('HIST_002', 'USER_003', 'WORD_001', 'TOPIC_001', 'mark_learned', NOW() - INTERVAL 2 HOUR + INTERVAL 45 SECOND, 'SESS_001', 3, 5),
('HIST_003', 'USER_003', 'WORD_002', 'TOPIC_001', 'start_learning', NOW() - INTERVAL 2 HOUR + INTERVAL 1 MINUTE, 'SESS_001', 0, 1),
('HIST_004', 'USER_004', 'WORD_019', 'TOPIC_004', 'start_learning', NOW() - INTERVAL 30 MINUTE, 'SESS_003', 0, 1),
('HIST_005', 'USER_004', 'WORD_019', 'TOPIC_004', 'mark_learned', NOW() - INTERVAL 30 MINUTE + INTERVAL 35 SECOND, 'SESS_003', 3, 5);

-- INSERT WORD_STUDY_HISTORY for user words
INSERT INTO word_study_history (history_id, user_id, user_word_id, topic_id, action_type, action_time, session_id, mastery_level_before, mastery_level_after) VALUES
('HIST_006', 'USER_003', 'UWORD_001', 'TOPIC_005', 'start_learning', NOW() - INTERVAL 15 MINUTE, 'SESS_004', 0, 1),
('HIST_007', 'USER_003', 'UWORD_001', 'TOPIC_005', 'mark_learned', NOW() - INTERVAL 15 MINUTE + INTERVAL 45 SECOND, 'SESS_004', 3, 5);

-- INSERT LEARNING_STATISTICS
INSERT INTO learning_statistics (stat_id, user_id, topic_id, date, total_words, learned_words, study_time, sessions_count, accuracy_rate, words_marked_learned, words_unmarked) VALUES
('STAT_001', 'USER_003', 'TOPIC_001', CURDATE(), 6, 2, 60, 1, 85.50, 2, 0),
('STAT_002', 'USER_003', 'TOPIC_002', CURDATE(), 6, 0, 30, 1, 0.00, 0, 0),
('STAT_003', 'USER_004', 'TOPIC_004', CURDATE(), 6, 1, 15, 1, 90.00, 1, 0),
('STAT_004', 'USER_003', 'TOPIC_005', CURDATE(), 3, 1, 15, 1, 100.00, 1, 0);

-- INSERT STUDY_MODES
INSERT INTO study_modes (mode_id, mode_name, description) VALUES
('MODE_001', 'flashcard', 'Học bằng thẻ flashcard hai mặt'),
('MODE_002', 'list_view', 'Xem danh sách từ truyền thống'),
('MODE_003', 'quiz', 'Làm bài tập trắc nghiệm'),
('MODE_004', 'review', 'Ôn tập từ đã học');

-- INSERT STUDY_SETTINGS
INSERT INTO study_settings (setting_id, user_id, topic_id, auto_mark_learned, auto_mark_threshold, show_learned_words, review_interval) VALUES
('SETT_001', 'USER_003', 'TOPIC_001', TRUE, 3, FALSE, 7),
('SETT_002', 'USER_003', 'TOPIC_002', FALSE, 5, TRUE, 14),
('SETT_003', 'USER_004', 'TOPIC_004', TRUE, 4, FALSE, 7),
('SETT_004', 'USER_003', 'TOPIC_005', TRUE, 2, FALSE, 3);

-- INSERT STUDY_NOTIFICATIONS
INSERT INTO study_notifications (notification_id, user_id, topic_id, notification_type, title, message, is_read) VALUES
('NOTIF_001', 'USER_003', 'TOPIC_001', 'review_reminder', 'Nhắc nhở ôn tập', 'Bạn có 5 từ cần ôn tập trong chủ đề "Từ vựng tiếng Anh văn phòng"', FALSE),
('NOTIF_002', 'USER_003', 'TOPIC_002', 'achievement', 'Thành tích mới', 'Chúc mừng! Bạn đã hoàn thành 50% chủ đề "Tiếng Anh giao tiếp cơ bản"', FALSE),
('NOTIF_003', 'USER_004', 'TOPIC_004', 'streak_reminder', 'Duy trì chuỗi học', 'Bạn đã học liên tiếp 3 ngày. Hãy tiếp tục duy trì!', TRUE);

-- INSERT LEARNING_ACHIEVEMENTS
INSERT INTO learning_achievements (achievement_id, user_id, topic_id, achievement_type, achievement_name, description, value, earned_at) VALUES
('ACH_001', 'USER_003', 'TOPIC_001', 'first_word', 'Bước đầu tiên', 'Học từ đầu tiên trong chủ đề', 1, NOW() - INTERVAL 2 HOUR),
('ACH_002', 'USER_003', 'TOPIC_001', 'word_streak', 'Chuỗi học tập', 'Học liên tiếp 3 từ đúng', 3, NOW() - INTERVAL 1 HOUR),
('ACH_003', 'USER_004', 'TOPIC_004', 'perfect_score', 'Hoàn hảo', 'Trả lời đúng 100% trong một phiên học', 100, NOW() - INTERVAL 30 MINUTE),
('ACH_004', 'USER_003', 'TOPIC_005', 'topic_completed', 'Hoàn thành chủ đề', 'Học xong tất cả từ trong chủ đề cá nhân', 3, NOW() - INTERVAL 15 MINUTE);

-- INSERT BATCH_IMPORTS
INSERT INTO batch_imports (import_id, user_id, topic_id, import_name, total_words, success_count, error_count, import_status, started_at, completed_at) VALUES
('IMPORT_001', 'USER_003', 'TOPIC_005', 'business_vocabulary.xlsx', 10, 8, 2, 'completed', NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY + INTERVAL 5 MINUTE),
('IMPORT_002', 'USER_004', 'TOPIC_006', 'travel_words.csv', 5, 5, 0, 'completed', NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 2 DAY + INTERVAL 2 MINUTE);

-- INSERT IMPORT_DETAILS
INSERT INTO import_details (detail_id, import_id, row_num, word, meaning_vi, part_of_speech, import_status, created_word_id) VALUES
-- Import 1 details
('IMP_DET_001', 'IMPORT_001', 1, 'revenue', 'doanh thu', 'noun', 'success', 'UWORD_001'),
('IMP_DET_002', 'IMPORT_001', 2, 'profit', 'lợi nhuận', 'noun', 'success', 'UWORD_002'),
('IMP_DET_003', 'IMPORT_001', 3, 'strategy', 'chiến lược', 'noun', 'success', 'UWORD_003'),
('IMP_DET_004', 'IMPORT_001', 4, 'invalid_word', '', '', 'error', NULL),
('IMP_DET_005', 'IMPORT_001', 5, 'another_invalid', '', '', 'error', NULL),

-- Import 2 details
('IMP_DET_006', 'IMPORT_002', 1, 'itinerary', 'lịch trình', 'noun', 'success', 'UWORD_004'),
('IMP_DET_007', 'IMPORT_002', 2, 'souvenir', 'quà lưu niệm', 'noun', 'success', 'UWORD_005');

-- INSERT REFRESH_TOKENS
INSERT INTO refresh_tokens (token_id, user_id, token_hash, device_info, ip_address, user_agent, expires_at) VALUES
('TOKEN_001', 'USER_003', '$2b$10$hashed_refresh_token_1', '{"browser": "Chrome", "os": "Windows 10"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() + INTERVAL 7 DAY),
('TOKEN_002', 'USER_004', '$2b$10$hashed_refresh_token_2', '{"browser": "Firefox", "os": "macOS"}', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', NOW() + INTERVAL 7 DAY);

-- INSERT LOGIN_HISTORY
INSERT INTO login_history (login_id, user_id, login_time, ip_address, user_agent, login_status, session_duration) VALUES
('LOGIN_001', 'USER_003', NOW() - INTERVAL 2 HOUR, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'success', 3600),
('LOGIN_002', 'USER_004', NOW() - INTERVAL 30 MINUTE, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', 'success', 1800),
('LOGIN_003', 'USER_003', NOW() - INTERVAL 1 DAY, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'success', 7200);

-- INSERT EMAIL_VERIFICATIONS
INSERT INTO email_verifications (verification_id, user_id, email, verification_token, is_verified, verified_at, expires_at) VALUES
('VERIFY_001', 'USER_003', '<EMAIL>', 'verification_token_123', TRUE, NOW() - INTERVAL 1 DAY, NOW() + INTERVAL 23 DAY),
('VERIFY_002', 'USER_004', '<EMAIL>', 'verification_token_456', TRUE, NOW() - INTERVAL 2 DAY, NOW() + INTERVAL 22 DAY);

-- INSERT OTP_CODES
INSERT INTO otp_codes (otp_id, user_id, email, otp_code, otp_type, expires_at) VALUES
('OTP_001', 'USER_003', '<EMAIL>', '123456', 'email_verification', NOW() + INTERVAL 10 MINUTE),
('OTP_002', 'USER_004', '<EMAIL>', '654321', 'password_reset', NOW() + INTERVAL 10 MINUTE);

-- INSERT PASSWORD_RESET_TOKENS
INSERT INTO password_reset_tokens (reset_id, user_id, reset_token, expires_at) VALUES
('RESET_001', 'USER_003', 'reset_token_123', NOW() + INTERVAL 1 HOUR),
('RESET_002', 'USER_004', 'reset_token_456', NOW() + INTERVAL 1 HOUR);

-- =============================================
-- CẬP NHẬT SỐ LƯỢNG TỪ TRONG TOPICS
-- =============================================
UPDATE topics SET word_count = (
    SELECT COUNT(*) FROM words WHERE words.topic_id = topics.topic_id
) WHERE topic_type = 'system';

UPDATE topics SET word_count = (
    SELECT COUNT(*) FROM user_words WHERE user_words.topic_id = topics.topic_id
) WHERE topic_type = 'user_created';

-- =============================================
-- TẠO INDEXES CƠ BẢN CHO PERFORMANCE
-- =============================================
CREATE INDEX idx_words_topic ON words(topic_id);
CREATE INDEX idx_user_words_topic ON user_words(topic_id);
CREATE INDEX idx_user_words_user ON user_words(user_id);
CREATE INDEX idx_progress_user_topic ON word_learning_progress(user_id, topic_id);
CREATE INDEX idx_sessions_user_topic ON study_sessions(user_id, topic_id);
CREATE INDEX idx_history_user_topic ON word_study_history(user_id, topic_id);
CREATE INDEX idx_notifications_user_read ON study_notifications(user_id, is_read);
CREATE INDEX idx_achievements_user ON learning_achievements(user_id);
CREATE INDEX idx_statistics_user_date ON learning_statistics(user_id, date);

-- =============================================
-- HOÀN THÀNH
-- =============================================
SELECT 'Database e_learnning2 đã được tạo thành công!' as message;
SELECT COUNT(*) as total_users FROM users;
SELECT COUNT(*) as total_topics FROM topics;
SELECT COUNT(*) as total_words FROM words;
SELECT COUNT(*) as total_user_words FROM user_words;



// có lỗi một chút nhưng ko sao
03:32:37	CREATE TABLE import_details (     detail_id VARCHAR(20) PRIMARY KEY COMMENT 'Khóa chính - ID chi tiết',     import_id VARCHAR(20) NOT NULL COMMENT 'Thuộc import nào',     row_number INT COMMENT 'Số thứ tự dòng trong file',     word VARCHAR(255) COMMENT 'Từ tiếng Anh',     meaning_vi TEXT COMMENT 'Nghĩa tiếng Việt',     part_of_speech VARCHAR(50) COMMENT 'Loại từ',     pronunciation VARCHAR(255) COMMENT 'Phiên âm',     example_en TEXT COMMENT 'Ví dụ tiếng Anh',     example_vi TEXT COMMENT 'Ví dụ tiếng Việt',     notes TEXT COMMENT 'Ghi chú',     import_status ENUM('pending', 'success', 'error') DEFAULT 'pending' COMMENT 'Trạng thái import từng từ',     error_message TEXT COMMENT 'Thông báo lỗi nếu có',     created_word_id VARCHAR(20) COMMENT 'ID từ được tạo thành công',     FOREIGN KEY (import_id) REFERENCES batch_imports(import_id) ON DELETE CASCADE,     FOREIGN KEY (created_word_id) REFERENCES user_words(user_word_id) ON DELETE SET NULL ) ENGINE=InnoDB	Error Code: 1064. You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'row_number INT COMMENT 'Số thứ tự dòng trong file',     word VARCHAR(255)' at line 4	0.000 sec
