{"name": "ecommerce-backend", "version": "1.0.0", "description": "E-commerce Backend API with Node.js and MySQL", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "dev:simple": "nodemon src/server-simple.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix"}, "keywords": ["ecommerce", "api", "nodejs", "mysql", "express"], "author": "Your Name", "license": "MIT", "dependencies": {"bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "gmail-send": "^1.8.14", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "multer": "^2.0.2", "mysql2": "^3.14.2", "sequelize": "^6.37.7"}, "devDependencies": {"eslint": "^9.32.0", "jest": "^30.0.5", "nodemon": "^3.1.10", "prettier": "^3.6.2", "supertest": "^7.1.4"}}