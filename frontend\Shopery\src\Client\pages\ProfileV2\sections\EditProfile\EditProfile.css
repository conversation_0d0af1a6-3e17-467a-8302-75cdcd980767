/* Container */
.section-card {
  background: #fff;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* Avatar */
.profilev2-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  font-weight: bold;
  background: #f3f4f6;
  color: #4b5563;
  margin-bottom: 15px;
}

/* Form layout */
.form-row {
  display: flex;
  flex-direction: column;
  gap: 15px;
  background: #fff;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-row label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.form-row input {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #111827;
  outline: none;
  transition: border-color 0.2s ease;
}

.form-row input:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 1px #4f46e5;
}

/* Buttons */
.actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  justify-content: flex-start;
}

.btn-primary {
  background-color: #4f46e5;
  color: white;
  border: none;
  padding: 10px 18px;
  font-size: 14px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.btn-primary:hover {
  background-color: #4338ca;
}

.btn-primary:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

/* File input */
.actions input[type="file"] {
  font-size: 14px;
  margin-top: 10px;
}
