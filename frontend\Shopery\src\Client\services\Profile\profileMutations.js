import { useMutation } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { profileApi } from "../../api/Profile/profileApi";

export const useUpdateProfile = () =>
  useMutation({
    mutationFn: profileApi.updateProfile,
    onSuccess: (data) => {
      const { EM, EC, DT } = data;
      if (EC === "0") toast.success(EM || "Profile updated");
      else toast.error(EM || "Update failed");
    },
    onError: (error) => {
      console.error("Update profile error:", error);
      // Xử lý lỗi validation 422
      if (error?.response?.status === 422) {
        const validationErrors = error.response.data?.errors;
        if (validationErrors && Array.isArray(validationErrors)) {
          const errorMessages = validationErrors.map(err => err.msg).join(", ");
          toast.error(`Validation error: ${errorMessages}`);
        } else {
          toast.error("<PERSON><PERSON> liệu không hợp lệ");
        }
      } else {
        const errorMessage = error?.response?.data?.EM || error?.response?.data?.message || "Request failed";
        toast.error(errorMessage);
      }
    },
  });

export const useChangePassword = () =>
  useMutation({
    mutationFn: profileApi.changePassword,
    onSuccess: (data) => {
      const { EM, EC } = data || {};
      if (EC === "0") toast.success(EM || "Password updated");
      else toast.error(EM || "Update failed");
    },
    onError: () => toast.error("Request failed"),
  });

export const useChangeEmail = () =>
  useMutation({
    mutationFn: profileApi.changeEmail,
    onSuccess: (data) => {
      const { EM, EC } = data || {};
      if (EC === "0") toast.success(EM || "Email updated");
      else toast.error(EM || "Update failed");
    },
    onError: () => toast.error("Request failed"),
  });

export const useUpdateLocalization = () =>
  useMutation({
    mutationFn: profileApi.updateLocalization,
    onSuccess: (data) => {
      const { EM, EC } = data || {};
      if (EC === "0") toast.success(EM || "Updated");
      else toast.error(EM || "Update failed");
    },
    onError: () => toast.error("Request failed"),
  });


