/* Global CSS - Styles toàn c<PERSON>c */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Colors */
  --primary-color: #007bff;
  --primary-dark: #0056b3;
  --secondary-color: #6c757d;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  
  /* Text Colors */
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-muted: #868e96;
  
  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-dark: #343a40;
  
  /* Border Colors */
  --border-color: #dee2e6;
  --border-light: #e9ecef;
  
  /* Shadows */
  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  
  /* Border Radius */
  --border-radius-sm: 0.25rem;
  --border-radius: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 1rem;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 3rem;
  
  /* Font Sizes */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  
  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-bottom: var(--spacing-md);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--text-primary);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Utility Classes */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.container-fluid {
  width: 100%;
  padding: 0 var(--spacing-md);
}

/* Text Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-success { color: var(--success-color); }
.text-danger { color: var(--danger-color); }
.text-warning { color: var(--warning-color); }

/* Background Utilities */
.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-light { background-color: var(--light-color); }
.bg-dark { background-color: var(--dark-color); }

/* Spacing Utilities */
.m-0 { margin: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }

/* Display Utilities */
.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-inline-flex { display: inline-flex; }

/* Flex Utilities */
.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }
.align-items-center { align-items: center; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }

.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-grow-1 { flex-grow: 1; }

/* Border Utilities */
.border { border: 1px solid var(--border-color); }
.border-top { border-top: 1px solid var(--border-color); }
.border-bottom { border-bottom: 1px solid var(--border-color); }
.border-0 { border: 0; }

.rounded { border-radius: var(--border-radius); }
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-circle { border-radius: 50%; }

/* Shadow Utilities */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* Position Utilities */
.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }
.position-sticky { position: sticky; }

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-sm);
  }
  
  h1 { font-size: var(--font-size-3xl); }
  h2 { font-size: var(--font-size-2xl); }
  h3 { font-size: var(--font-size-xl); }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Tùy chỉnh Toastify */
.Toastify__toast-container {
  z-index: 9999; /* Đảm bảo toast hiển thị trên cùng */
  font-family: 'Inter', sans-serif; /* Font tùy chỉnh, thay bằng font của dự án */
}

/* Toast chung */
.Toastify__toast {
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  line-height: 1.5;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-height: 60px;
  /* Gradient chung cho background */
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(245, 245, 245, 0.9));
}

/* Success Toast */
.Toastify__toast--success {
  color: #1a3c34; /* Màu chữ tối */
  border: 1px solid transparent; /* Viền mỏng */
  background: linear-gradient(135deg, #e6f4ea 0%, #c8e6c9 100%); /* Gradient xanh lá nhạt */
}

/* Error Toast */
.Toastify__toast--error {
  color: #5f2120; /* Màu chữ tối */
  border: 1px solid transparent; /* Viền mỏng */
  background: linear-gradient(135deg, #fce7e7 0%, #f8d7da 100%); /* Gradient đỏ nhạt */
}

/* Warning Toast */
.Toastify__toast--warning {
  color: #663c00; /* Màu chữ tối */
  border: 1px solid transparent; /* Viền mỏng */
  background: linear-gradient(135deg, #fff4e5 0%, #ffe4b5 100%); /* Gradient vàng cam nhạt */
}

/* Info Toast */
.Toastify__toast--info {
  color: #1e3a5f; /* Màu chữ tối */
  border: 1px solid transparent; /* Viền mỏng */
  background: linear-gradient(135deg, #e6f0fa 0%, #cce5ff 100%); /* Gradient xanh dương nhạt */
}

/* Tùy chỉnh progress bar */
.Toastify__progress-bar {
  height: 4px;
}

.Toastify__progress-bar--success {
  background: linear-gradient(90deg, #4caf50 0%, #2e7d32 100%); /* Gradient xanh */
}

.Toastify__progress-bar--error {
  background: linear-gradient(90deg, #f44336 0%, #c62828 100%); /* Gradient đỏ */
}

.Toastify__progress-bar--warning {
  background: linear-gradient(90deg, #ff9800 0%, #f57c00 100%); /* Gradient cam */
}

.Toastify__progress-bar--info {
  background: linear-gradient(90deg, #2196f3 0%, #1976d2 100%); /* Gradient xanh dương */
}

/* Tùy chỉnh close button */
.Toastify__close-button {
  color: #333;
  opacity: 0.7;
}

.Toastify__close-button:hover {
  opacity: 1;
}

/* Tùy chỉnh description (nếu dùng) */
.Toastify__toast-body > div:last-child {
  font-size: 12px;
  opacity: 0.8;
}

/* Animation tùy chỉnh */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.Toastify__toast--enter {
  animation: slideIn 0.3s ease-out;
}

.Toastify__toast--exit {
  animation: slideOut 0.3s ease-in;
}