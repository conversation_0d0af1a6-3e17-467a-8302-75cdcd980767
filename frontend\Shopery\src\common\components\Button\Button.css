/* Button Component Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  font-family: inherit;
  position: relative;
  overflow: hidden;
}

/* Sizes */
.btn--small {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
  min-height: 36px;
}

.btn--medium {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-base);
  min-height: 44px;
}

.btn--large {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
  min-height: 52px;
}

/* Variants */
.btn--primary {
  background-color: var(--primary-color);
  color: white;
}

.btn--primary:hover:not(.btn--disabled) {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
}

.btn--secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn--secondary:hover:not(.btn--disabled) {
  background-color: #545b62;
}

.btn--success {
  background-color: var(--success-color);
  color: white;
}

.btn--success:hover:not(.btn--disabled) {
  background-color: #1e7e34;
}

.btn--danger {
  background-color: var(--danger-color);
  color: white;
}

.btn--danger:hover:not(.btn--disabled) {
  background-color: #c82333;
}

.btn--outline {
  background-color: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
}

.btn--outline:hover:not(.btn--disabled) {
  background-color: var(--primary-color);
  color: white;
}

/* States */
.btn--disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn--loading {
  cursor: wait;
}

.btn__spinner {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
