// Main App Component
import React from "react";
import { Provider } from "react-redux";
import { <PERSON>rowser<PERSON>outer as Router } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import "./assets/styles/global.css"; // CSS tùy chỉnh
import { QueryProvider } from "./providers/QueryProvider";
import { store } from "./redux/store";
import AppRoutes from "./routes/routes";
// Import global styles
import "./App.css";
import "./assets/styles/global.css";
import "./assets/styles/reset.css";

function App() {
  return (
    <Provider store={store}>
      <QueryProvider>
        <Router>
          <div className="App">
            <AppRoutes />
          </div>
        </Router>
      </QueryProvider>

      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </Provider>
  );
}

export default App;
