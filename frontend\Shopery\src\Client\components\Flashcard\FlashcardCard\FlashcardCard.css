/* Client/components/Flashcard/FlashcardCard/FlashcardCard.css */
.flashcard-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  min-height: 200px;
  max-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.flashcard-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.create-card {
  border: 2px dashed #d1d5db;
  background: #f9fafb;
  justify-content: center;
  align-items: center;
  text-align: center;
  min-height: 200px;
  max-height: 200px;
}

.create-card:hover {
  border-color: #3b82f6;
  background: #eff6ff;
}

.create-card-icon {
  font-size: 32px;
  color: #3b82f6;
  margin-bottom: 8px;
}

.create-card-text {
  color: #3b82f6;
  font-weight: 500;
}

.card-header {
  margin-bottom: 12px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-stats {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}

.word-count {
  font-weight: 500;
  color: #3b82f6;
}

.view-count {
  color: #9ca3af;
}

.card-description {
  color: #6b7280;
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 12px;
  flex-grow: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-user {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: auto;
}

.user-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3b82f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
  font-weight: 500;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-size: 10px;
  color: #6b7280;
}

.card-logo {
  position: absolute;
  bottom: 12px;
  right: 12px;
}

.card-logo img {
  height: 16px;
  opacity: 0.7;
}

  /* Client/pages/Flashcard/Flashcard.css (thêm vào) */
.empty-learning {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    color: #6b7280;
    font-size: 16px;
    line-height: 1.5;
  }