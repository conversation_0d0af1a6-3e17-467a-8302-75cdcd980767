/* Client/components/Flashcard/FlashcardDetail/FlashcardDetail.css */
.flashcard-detail {
  max-width: 1000px;
  margin: 0 auto;
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  padding-top: 100px; /* Thêm padding-top để tránh bị header đè */
}

.detail-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;
  background: white;
  padding: 20px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.back-btn {
  background: linear-gradient(135deg, #4fd1c7 0%, #38b2ac 100%);
  border: none;
  color: white;
  cursor: pointer;
  font-size: 14px;
  padding: 10px 16px;
  border-radius: 25px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(79, 209, 199, 0.3);
}

.back-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(79, 209, 199, 0.4);
}

.detail-title {
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #4fd1c7 0%, #38b2ac 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

/* Study Mode Toggle */
.study-mode-toggle {
  display: flex;
  gap: 8px;
  margin-bottom: 32px;
  background: white;
  padding: 6px;
  border-radius: 50px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.mode-btn {
  flex: 1;
  padding: 14px 20px;
  border: none;
  background: transparent;
  border-radius: 40px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 600;
  color: #64748b;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1; /* Đảm bảo text hiển thị trên background */
}

.mode-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #4fd1c7 0%, #38b2ac 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 40px;
  z-index: -1; /* Background ở dưới text */
}

.mode-btn:hover {
  color: #1e293b;
  transform: translateY(-1px);
}

.mode-btn.active {
  color: white;
  box-shadow: 0 4px 15px rgba(79, 209, 199, 0.3);
}

.mode-btn.active::before {
  opacity: 1;
}

.mode-btn span {
  position: relative;
  z-index: 2; /* Text luôn ở trên cùng */
  display: inline-block;
  width: 100%;
  text-align: center;
}

.detail-actions {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 32px;
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.practice-btn {
  background: linear-gradient(135deg, #FF6B35 0%, #f97316 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  align-self: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.practice-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

.action-links {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.action-link {
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 20px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.action-link:hover {
  color: #1e293b;
  background: #f1f5f9;
}

.stop-btn {
  color: #ef4444;
}

.stop-btn:hover {
  color: #dc2626;
  background: #fef2f2;
}

.link-icon {
  font-size: 14px;
}

.word-count-info {
  color: #64748b;
  font-size: 16px;
  margin-bottom: 32px;
  text-align: center;
  background: white;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  font-weight: 500;
}

/* Word List Mode */
.word-list-container {
  margin-bottom: 32px;
}

.word-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.word-list-item {
  display: flex;
  gap: 20px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.word-list-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #4fd1c7 0%, #38b2ac 100%);
}

.word-list-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #4fd1c7;
}

.word-list-number {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #4fd1c7 0%, #38b2ac 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 16px;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(79, 209, 199, 0.3);
}

.word-list-content {
  flex: 1;
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.word-list-text {
  flex: 1;
}

.word-list-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.word-list-word {
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  background: linear-gradient(135deg, #4fd1c7 0%, #38b2ac 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.word-list-type {
  color: #64748b;
  font-size: 14px;
  background: #f1f5f9;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.word-list-pronunciation {
  color: #64748b;
  font-size: 14px;
  font-family: 'Courier New', monospace;
  background: #f8fafc;
  padding: 4px 8px;
  border-radius: 8px;
}

.word-list-definition {
  color: #374151;
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 16px 0;
  font-weight: 500;
}

.word-list-example {
  margin-bottom: 16px;
}

.word-list-example .example-en {
  font-size: 15px;
  color: #1e293b;
  margin: 0 0 6px 0;
  font-style: italic;
  font-weight: 500;
}

.word-list-example .example-vi {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

.word-list-image {
  width: 140px;
  height: 100px;
  border-radius: 12px;
  overflow: hidden;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 2px solid #e2e8f0;
}

.word-list-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.word-list-image:hover img {
  transform: scale(1.05);
}

/* Flashcard Mode */
.flashcard-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;
  background: white;
  padding: 32px;
  border-radius: 20px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.flashcard-progress {
  color: #64748b;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #4fd1c7 0%, #38b2ac 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.flashcard {
  width: 100%;
  max-width: 550px;
  height: 350px;
  perspective: 1200px;
  cursor: pointer;
}

.flashcard-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-style: preserve-3d;
}

.flashcard-inner.flipped {
  transform: rotateY(180deg);
}

.flashcard-front,
.flashcard-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 2px solid #e2e8f0;
  border-radius: 20px;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.flashcard-back {
  transform: rotateY(180deg);
  background: linear-gradient(135deg, #4fd1c7 0%, #38b2ac 100%);
  color: white;
  border-color: #4fd1c7;
}

.word-section {
  margin-bottom: 20px;
}

.word {
  font-size: 36px;
  font-weight: 800;
  color: #1e293b;
  margin: 0 0 12px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.flashcard-back .word {
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.word-type {
  color: #64748b;
  font-size: 18px;
  font-weight: 600;
  background: #f1f5f9;
  padding: 6px 12px;
  border-radius: 20px;
}

.flashcard-back .word-type {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.2);
}

.pronunciation-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 32px;
}

.pronunciation {
  font-size: 20px;
  color: #64748b;
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.flashcard-back .pronunciation {
  color: rgba(255, 255, 255, 0.9);
}

.audio-btn {
  background: linear-gradient(135deg, #4fd1c7 0%, #38b2ac 100%);
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: white;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(79, 209, 199, 0.3);
}

.audio-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(79, 209, 199, 0.4);
}

.flip-hint {
  color: #94a3b8;
  font-size: 16px;
  font-weight: 500;
  margin-top: 20px;
}

.flashcard-back .flip-hint {
  color: rgba(255, 255, 255, 0.8);
}

.definition-title,
.example-title {
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 16px 0;
}

.flashcard-back .definition-title,
.flashcard-back .example-title {
  color: white;
}

.definition {
  font-size: 18px;
  color: #374151;
  line-height: 1.7;
  margin-bottom: 24px;
  font-weight: 500;
}

.flashcard-back .definition {
  color: rgba(255, 255, 255, 0.95);
}

.example {
  text-align: left;
  width: 100%;
}

.example-en {
  font-size: 18px;
  color: #1e293b;
  margin: 0 0 10px 0;
  font-weight: 500;
}

.flashcard-back .example-en {
  color: rgba(255, 255, 255, 0.95);
}

.example-vi {
  font-size: 16px;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

.flashcard-back .example-vi {
  color: rgba(255, 255, 255, 0.8);
}

.word-image {
  width: 220px;
  height: 160px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border: 3px solid #e2e8f0;
  margin-top: 20px;
}

.word-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.word-image:hover img {
  transform: scale(1.05);
}

.flashcard-controls {
  display: flex;
  gap: 20px;
}

.control-btn {
  padding: 14px 28px;
  border: 2px solid #e2e8f0;
  background: white;
  border-radius: 50px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 600;
  transition: all 0.3s ease;
  color: #64748b;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.control-btn:hover:not(:disabled) {
  background: #f8fafc;
  border-color: #4fd1c7;
  color: #1e293b;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(79, 209, 199, 0.2);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.flip-btn {
  background: linear-gradient(135deg, #4fd1c7 0%, #38b2ac 100%);
  color: white;
  border-color: #4fd1c7;
  box-shadow: 0 4px 15px rgba(79, 209, 199, 0.3);
}

.flip-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(79, 209, 199, 0.4);
}

.word-actions {
  display: flex;
  gap: 16px;
}

.word-action-btn {
  padding: 12px 20px;
  border: 2px solid #e2e8f0;
  background: white;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  color: #64748b;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.word-action-btn:hover {
  background: #f8fafc;
  border-color: #4fd1c7;
  color: #1e293b;
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(79, 209, 199, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .flashcard-detail {
    padding: 16px;
    padding-top: 80px; /* Giảm padding-top trên mobile */
  }
  
  .detail-title {
    font-size: 24px;
  }
  
  .word-list-item {
    flex-direction: column;
    gap: 16px;
  }
  
  .word-list-content {
    flex-direction: column;
    gap: 16px;
  }
  
  .word-list-image {
    width: 100%;
    height: 120px;
  }
  
  .flashcard {
    height: 300px;
  }
  
  .flashcard-front,
  .flashcard-back {
    padding: 24px;
  }
  
  .word {
    font-size: 28px;
  }
  
  .flashcard-controls {
    flex-direction: column;
    gap: 12px;
  }
  
  .control-btn {
    width: 100%;
  }
}