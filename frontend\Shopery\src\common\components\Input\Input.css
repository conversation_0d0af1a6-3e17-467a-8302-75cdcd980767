/* Input Component Styles */
.input-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  width: 100%;
}

.input-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.input-required {
  color: var(--danger-color);
  margin-left: 2px;
}

.input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  font-family: inherit;
  background-color: var(--bg-primary);
  transition: all var(--transition-fast);
  outline: none;
}

.input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.input::placeholder {
  color: var(--text-muted);
}

.input--error {
  border-color: var(--danger-color);
}

.input--error:focus {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.input--disabled {
  background-color: var(--bg-secondary);
  color: var(--text-muted);
  cursor: not-allowed;
}

.input-error-message {
  font-size: var(--font-size-sm);
  color: var(--danger-color);
  margin-top: var(--spacing-xs);
}

.input-helper-text {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
}
