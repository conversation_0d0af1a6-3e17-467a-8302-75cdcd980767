/* Footer Component - CSS theo chuẩn BEM */

.footer {
  background: #2D3748;
  color: white;
  padding: 60px 0 20px;
  margin-top: auto;
}

.footer__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.footer__content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer__section {
  display: flex;
  flex-direction: column;
}

.footer__title {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 16px 0;
  color: #4FD1C7;
}

.footer__description {
  font-size: 14px;
  line-height: 1.6;
  color: #A0AEC0;
  margin: 0;
}

.footer__subtitle {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: white;
}

.footer__links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer__links li {
  margin-bottom: 8px;
}

.footer__links a {
  color: #A0AEC0;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer__links a:hover {
  color: #4FD1C7;
}

.footer__contact p {
  margin: 8px 0;
  font-size: 14px;
  color: #A0AEC0;
}

.footer__bottom {
  border-top: 1px solid #4A5568;
  padding-top: 20px;
  text-align: center;
}

.footer__copyright {
  font-size: 14px;
  color: #A0AEC0;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer {
    padding: 40px 0 20px;
  }

  .footer__content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .footer__title {
    font-size: 20px;
  }

  .footer__subtitle {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .footer__container {
    padding: 0 16px;
  }

  .footer__content {
    gap: 24px;
  }
}
