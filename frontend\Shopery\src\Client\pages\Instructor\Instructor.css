/* Instructor Page - CSS theo design system và layout Coursiva */
:root {
  --main-black: #181818;
  --main-gray: #888;
  --main-orange: #ff9800;
  --main-yellow: #ffc107;
  --main-border: #e5e7eb;
  --main-bg: #fafbfc;
  --main-white: #fff;
  --main-radius: 18px;
  --main-shadow: 0 2px 12px rgba(0,0,0,0.06);
  
  /* Instructor specific colors */
  --instructor-primary: #4fd1c7;
  --instructor-accent: #FF6B35;
  --instructor-gradient: linear-gradient(135deg, #4fd1c7 0%, #38b2ac 100%);
  --instructor-light: #f0f9ff;
}

.instructor-page {
  min-height: 100vh;
  background: var(--main-bg);
  padding: 90px 0 2rem 0; /* Tránh header fixed */
}

.instructor-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Hero Section */
.instructor-hero {
  background: var(--instructor-gradient);
  border-radius: var(--main-radius);
  padding: 60px 40px;
  margin-bottom: 40px;
  position: relative;
  overflow: hidden;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: center;
  position: relative;
  z-index: 2;
}

.hero-text h1 {
  font-size: 3rem;
  font-weight: 700;
  color: white;
  margin: 0 0 16px 0;
  line-height: 1.2;
  letter-spacing: -1px;
}

.hero-text p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.6;
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-image img {
  width: 100%;
  max-width: 400px;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Filter Bar */
.instructor-filter {
  margin-bottom: 40px;
}

.filter-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 12px 24px;
  border: 2px solid var(--main-border);
  background: var(--main-white);
  color: var(--main-black);
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.filter-btn:hover {
  border-color: var(--instructor-primary);
  color: var(--instructor-primary);
  transform: translateY(-2px);
}

.filter-btn.active {
  background: var(--instructor-accent);
  border-color: var(--instructor-accent);
  color: white;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

/* Instructor Grid */
.instructor-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-bottom: 40px;
}

/* Instructor Card Link */
.instructor-card-link {
  text-decoration: none;
  color: inherit;
  display: block;
}

/* Instructor Card */
.instructor-card {
  background: var(--main-white);
  border-radius: 12px;
  padding: 20px;
  box-shadow: var(--main-shadow);
  border: 1px solid var(--main-border);
  transition: all 0.3s ease;
  text-align: center;
  height: 100%;
}

.instructor-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: var(--instructor-primary);
}

/* Instructor Image */
.instructor-image {
  width: 120px;
  height: 120px;
  margin: 0 auto 16px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid var(--instructor-light);
  transition: border-color 0.3s ease;
}

.instructor-card:hover .instructor-image {
  border-color: var(--instructor-primary);
}

.instructor-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Instructor Info */
.instructor-info {
  text-align: center;
}

.instructor-name {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--main-black);
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.instructor-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 0.9rem;
  color: var(--main-gray);
  font-weight: 500;
}

.instructor-title i {
  font-size: 0.8rem;
  color: var(--instructor-primary);
  transition: transform 0.3s ease;
}

.instructor-card:hover .instructor-title i {
  transform: translateY(2px);
}

/* Rating */
.instructor-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 8px;
}

.stars {
  display: flex;
  gap: 2px;
}

.stars i {
  font-size: 0.9rem;
  color: #e4e5e9;
  transition: color 0.2s ease;
}

.stars i.filled {
  color: var(--main-yellow);
}

.rating-text {
  font-size: 0.85rem;
  color: var(--main-gray);
  font-weight: 500;
}

/* Pagination */
.instructor-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
}

.pagination-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: var(--instructor-primary);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-btn:hover {
  background: var(--instructor-accent);
  transform: scale(1.1);
}

.pagination-btn:disabled {
  background: var(--main-border);
  cursor: not-allowed;
  transform: none;
}

.pagination-info {
  font-size: 1rem;
  color: var(--main-black);
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .instructor-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }
  
  .hero-content {
    gap: 30px;
  }
  
  .hero-text h1 {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .instructor-page {
    padding: 80px 0 1rem 0;
  }
  
  .instructor-container {
    padding: 0 16px;
  }
  
  .instructor-hero {
    padding: 40px 20px;
    margin-bottom: 30px;
  }
  
  .hero-content {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }
  
  .hero-text h1 {
    font-size: 2rem;
  }
  
  .hero-text p {
    font-size: 1rem;
  }
  
  .filter-buttons {
    justify-content: center;
    gap: 8px;
  }
  
  .filter-btn {
    padding: 10px 16px;
    font-size: 0.9rem;
  }
  
  .instructor-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .instructor-card {
    padding: 16px;
  }
  
  .instructor-image {
    width: 100px;
    height: 100px;
  }
  
  .instructor-name {
    font-size: 1rem;
  }
  
  .instructor-title {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .instructor-page {
    padding: 75px 0 1rem 0;
  }
  
  .instructor-hero {
    padding: 30px 16px;
  }
  
  .hero-text h1 {
    font-size: 1.8rem;
  }
  
  .filter-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .filter-btn {
    width: 100%;
    max-width: 200px;
  }
  
  .instructor-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .instructor-card {
    padding: 20px;
  }
  
  .instructor-image {
    width: 120px;
    height: 120px;
  }
  
  .instructor-pagination {
    gap: 16px;
  }
  
  .pagination-btn {
    width: 36px;
    height: 36px;
    font-size: 0.9rem;
  }
  
  .pagination-info {
    font-size: 0.9rem;
  }
}

/* Animation cho hero section */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-text {
  animation: fadeInUp 0.8s ease-out;
}

.hero-image {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

/* Hover effects cho cards */
.instructor-card {
  position: relative;
  overflow: hidden;
}

.instructor-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(79, 209, 199, 0.1), transparent);
  transition: left 0.5s ease;
}

.instructor-card:hover::before {
  left: 100%;
}

/* Loading state cho images */
.instructor-image img {
  transition: opacity 0.3s ease;
}

.instructor-image img:not([src]) {
  opacity: 0;
}

/* Focus states cho accessibility */
.filter-btn:focus,
.pagination-btn:focus {
  outline: 2px solid var(--instructor-primary);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .instructor-hero {
    background: none;
    color: black;
  }
  
  .filter-buttons {
    display: none;
  }
  
  .instructor-pagination {
    display: none;
  }
}
