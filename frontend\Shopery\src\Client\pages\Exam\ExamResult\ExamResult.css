/* Exam Result Page Styles */
.exam-result {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 2rem 0;
}

.exam-result__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Loading State */
.exam-result-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  color: #181818;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.exam-result-error {
  text-align: center;
  color: #181818;
  padding: 4rem 2rem;
}

.exam-result-error h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.exam-result-error p {
  margin-bottom: 2rem;
  color: #888;
}

/* Header - Study4 Style */
.exam-result__header {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 2rem;
}

.result-breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #888;
}

.breadcrumb-link {
  color: #181818;
  text-decoration: none;
  transition: color 0.3s ease;
}

.breadcrumb-link:hover {
  color: #181818;
}

.result-breadcrumb svg {
  color: #888;
}

.result-title h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #181818;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.exam-name {
  font-size: 1.25rem;
  color: #181818;
  margin: 0 0 0.25rem 0;
}

.completion-date {
  font-size: 1rem;
  color: #888;
  margin: 0;
}

.result-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.btn-primary,
.btn-secondary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Overview */
.exam-result__overview {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
  margin-bottom: 3rem;
}

.score-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.score-main {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 1rem;
}

.score-value {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1;
}

.score-max {
  font-size: 2rem;
  color: #888;
  margin-left: 0.5rem;
}

.score-details {
  margin-bottom: 1.5rem;
}

.score-percentage {
  font-size: 2rem;
  font-weight: 700;
  display: block;
  margin-bottom: 0.5rem;
}

.score-label {
  font-size: 1.125rem;
  color: #888;
  font-weight: 500;
}

.score-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-weight: 600;
  font-size: 1.125rem;
}

.status-passed {
  color: #10B981;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-failed {
  color: #EF4444;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.time-info {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.time-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: #181818;
}

.time-item:last-child {
  border-bottom: none;
}

.time-label {
  font-size: 1rem;
  color: #888;
}

.time-value {
  font-size: 1.125rem;
  font-weight: 600;
}

/* Sections */
.exam-result__sections {
  margin-bottom: 3rem;
}

.exam-result__sections h2 {
  font-size: 2rem;
  font-weight: 700;
  color: #181818;
  text-align: center;
  margin-bottom: 2rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.sections-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.section-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #F3F4F6;
}

.section-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #181818;
  margin: 0;
}

.section-score {
  font-size: 1.5rem;
  font-weight: 700;
}

.section-details {
  margin-bottom: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  font-size: 0.875rem;
  color: #888;
}

.detail-item span:first-child {
  font-weight: 500;
}

.section-progress {
  margin-top: 1rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #E5E7EB;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
}

/* Detailed Results */
.exam-result__detailed {
  margin-bottom: 3rem;
}

.exam-result__detailed h2 {
  font-size: 2rem;
  font-weight: 700;
  color: #181818;
  text-align: center;
  margin-bottom: 2rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.detailed-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.detailed-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #181818;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #F3F4F6;
}

.parts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.part-card {
  background: #F9FAFB;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #E5E7EB;
}

.part-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.part-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.part-percentage {
  font-size: 1.125rem;
  font-weight: 700;
}

.part-stats {
  font-size: 0.75rem;
  color: #888;
  margin-bottom: 0.75rem;
}

.part-progress {
  margin-top: 0.75rem;
}

/* Analysis */
.exam-result__analysis {
  margin-bottom: 3rem;
}

.exam-result__analysis h2 {
  font-size: 2rem;
  font-weight: 700;
  color: #181818;
  text-align: center;
  margin-bottom: 2rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.analysis-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.analysis-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #F3F4F6;
}

.analysis-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.analysis-card li {
  padding: 0.75rem 0;
  border-bottom: 1px solid #F3F4F6;
  line-height: 1.5;
  position: relative;
  padding-left: 1.5rem;
}

.analysis-card li:last-child {
  border-bottom: none;
}

.strengths h3 {
  color: #10B981;
  border-bottom-color: #D1D5DB;
}

.strengths li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #10B981;
  font-weight: bold;
}

.weaknesses h3 {
  color: #EF4444;
  border-bottom-color: #D1D5DB;
}

.weaknesses li::before {
  content: "⚠";
  position: absolute;
  left: 0;
  color: #EF4444;
  font-weight: bold;
}

.recommendations h3 {
  color: #181818;
  border-bottom-color: #D1D5DB;
}

.recommendations li::before {
  content: "💡";
  position: absolute;
  left: 0;
}

/* History */
.exam-result__history {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.exam-result__history h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #181818;
  margin-bottom: 1.5rem;
  text-align: left;
}

.history-chart {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.history-item {
  display: grid;
  grid-template-columns: 150px 1fr 200px;
  gap: 1rem;
  align-items: center;
  padding: 1rem;
  background: #F9FAFB;
  border-radius: 8px;
  border: 1px solid #E5E7EB;
}

.history-date {
  font-size: 0.875rem;
  color: #888;
  font-weight: 500;
}

.history-score {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
}

.history-score .score {
  font-size: 1.25rem;
  font-weight: 700;
  color: #181818;
}

.history-score .percentage {
  font-size: 0.875rem;
  color: #888;
}

.history-progress {
  width: 100%;
}

/* Buttons */
.btn-primary,
.btn-secondary {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  cursor: pointer;
  display: inline-block;
}

.btn-primary {
  background: white;
  color: #181818;
  border-color: #181818;
}

.btn-primary:hover {
  background: #ffffff;
  color: #181818;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.btn-secondary {
  background: transparent;
  color: #181818;
  border-color: rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

/* Responsive */
@media (max-width: 768px) {
  .exam-result__container {
    padding: 0 0.5rem;
  }

  .exam-result__header {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .result-title h1 {
    font-size: 2rem;
  }

  .exam-result__overview {
    grid-template-columns: 1fr;
  }

  .sections-grid,
  .analysis-grid {
    grid-template-columns: 1fr;
  }

  .parts-grid {
    grid-template-columns: 1fr;
  }

  .history-item {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    text-align: center;
  }

  .result-actions {
    flex-direction: column;
    width: 100%;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
  }
}
