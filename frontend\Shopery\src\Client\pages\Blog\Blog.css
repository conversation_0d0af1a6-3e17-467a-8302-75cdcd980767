/* Blog Page - CSS theo design system và layout mới */
:root {
  --main-black: #181818;
  --main-gray: #888;
  --main-orange: #ff9800;
  --main-yellow: #ffc107;
  --main-border: #e5e7eb;
  --main-bg: #fafbfc;
  --main-white: #fff;
  --main-radius: 18px;
  --main-shadow: 0 2px 12px rgba(0,0,0,0.06);
  
  /* Blog specific colors */
  --blog-primary: #4fd1c7;
  --blog-accent: #FF6B35;
  --blog-gradient: linear-gradient(135deg, #4fd1c7 0%, #38b2ac 100%);
}

.blog-page {
  min-height: 100vh;
  background: var(--main-bg);
  padding: 90px 0 2rem 0; /* Thêm padding-top để tránh header fixed */
}

.blog-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Header Section */
.blog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
  padding: 0 24px;
}

.blog-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  color: var(--main-black);
  letter-spacing: -1px;
}

.blog-search {
  position: relative;
  max-width: 300px;
}

.blog-search input {
  width: 100%;
  padding: 12px 40px 12px 16px;
  border-radius: 8px;
  border: 1px solid var(--main-border);
  font-size: 1rem;
  background: var(--main-white);
  color: var(--main-black);
  transition: border 0.2s;
}

.blog-search input:focus {
  border: 1px solid var(--blog-primary);
  outline: none;
}

.blog-search-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--main-gray);
  font-size: 1.1rem;
}

/* Main Layout */
.blog-main {
  display: flex;
  gap: 40px;
  padding: 0 24px;
}

/* Main Content */
.blog-content {
  flex: 2;
}

/* Blog Posts Grid */
.blog-posts {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-bottom: 40px;
}

.no-posts {
  color: var(--main-gray);
  font-size: 1.1rem;
  text-align: center;
  padding: 40px 0;
  grid-column: 1 / -1;
}

/* Blog Post Card */
.blog-post {
  background: var(--main-white);
  border-radius: 12px;
  box-shadow: var(--main-shadow);
  overflow: hidden;
  transition: box-shadow 0.2s, transform 0.2s;
  border: 1px solid var(--main-border);
}

.blog-post:hover {
  box-shadow: 0 4px 24px rgba(0,0,0,0.10);
  transform: translateY(-2px);
}

/* Post Image */
.blog-post-image {
  position: relative;
  width: 100%;
  aspect-ratio: 16/9;
  overflow: hidden;
  background: #eee;
}

.blog-post-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: transform 0.3s ease;
}

.blog-post:hover .blog-post-image img {
  transform: scale(1.05);
}

/* Post Content */
.blog-post-content {
  padding: 20px;
}

/* Post Title */
.blog-post-title {
  font-size: 1.2rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: var(--main-black);
  line-height: 1.4;
}

/* Post Date */
.blog-post-date {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  color: var(--main-gray);
  margin-bottom: 12px;
}

.blog-post-date i {
  font-size: 0.8rem;
}

/* Post Excerpt */
.blog-post-excerpt {
  color: var(--main-gray);
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
}

/* Pagination */
.blog-pagination {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 40px;
}

.pagination-btn {
  width: 40px;
  height: 40px;
  border: 1px solid var(--main-border);
  background: var(--main-white);
  border-radius: 50%;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--main-black);
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-btn:hover {
  border-color: var(--blog-primary);
  color: var(--blog-primary);
}

.pagination-btn.active {
  background: var(--main-black);
  color: var(--main-white);
  border-color: var(--main-black);
}

/* Right Sidebar */
.blog-sidebar {
  flex: 1;
  max-width: 300px;
}

.sidebar-section {
  background: var(--main-white);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: var(--main-shadow);
  border: 1px solid var(--main-border);
}

.sidebar-title {
  font-size: 1.2rem;
  font-weight: 700;
  margin: 0 0 20px 0;
  color: var(--main-black);
}

/* Category Section */
.category-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  cursor: pointer;
  transition: color 0.2s;
  border-bottom: 1px solid #f0f0f0;
}

.category-item:last-child {
  border-bottom: none;
}

.category-item:hover {
  color: var(--blog-primary);
}

.category-item.active {
  color: var(--blog-primary);
  font-weight: 600;
}

.category-name {
  font-size: 0.95rem;
  color: inherit;
}

.category-count {
  font-size: 0.9rem;
  color: var(--main-gray);
  font-weight: 500;
}

/* Recent Posts Section */
.recent-posts {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.recent-post {
  display: flex;
  gap: 12px;
  cursor: pointer;
  transition: transform 0.2s;
}

.recent-post:hover {
  transform: translateX(4px);
}

.recent-post-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.recent-post-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.recent-post-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.recent-post-title {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: var(--main-black);
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recent-post-date {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
  color: var(--main-gray);
}

.recent-post-date i {
  font-size: 0.7rem;
}

/* Tags Section */
.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  background: #f8f9fa;
  color: var(--main-gray);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid var(--main-border);
  cursor: pointer;
  transition: all 0.2s;
}

.tag-item:hover {
  background: var(--blog-primary);
  color: var(--main-white);
  border-color: var(--blog-primary);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .blog-main {
    flex-direction: column;
    gap: 32px;
  }
  
  .blog-sidebar {
    max-width: 100%;
  }
  
  .blog-posts {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .blog-page {
    padding: 80px 0 1rem 0; /* Giảm padding-top cho mobile */
  }
  
  .blog-container {
    padding: 0 16px;
  }
  
  .blog-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
    padding: 0 16px;
  }
  
  .blog-header h1 {
    font-size: 2rem;
  }
  
  .blog-search {
    max-width: 100%;
    width: 100%;
  }
  
  .blog-main {
    padding: 0 16px;
  }
  
  .blog-posts {
    gap: 20px;
  }
  
  .blog-post-content {
    padding: 16px;
  }
  
  .blog-post-title {
    font-size: 1.1rem;
  }
  
  .sidebar-section {
    padding: 20px;
  }
  
  .recent-post {
    gap: 10px;
  }
  
  .recent-post-image {
    width: 50px;
    height: 50px;
  }
  
  .recent-post-title {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .blog-page {
    padding: 75px 0 1rem 0; /* Điều chỉnh cho mobile nhỏ */
  }
  
  .blog-header h1 {
    font-size: 1.8rem;
  }
  
  .blog-post-content {
    padding: 12px;
  }
  
  .blog-post-title {
    font-size: 1rem;
  }
  
  .sidebar-section {
    padding: 16px;
  }
  
  .sidebar-title {
    font-size: 1.1rem;
  }
  
  .tag-item {
    font-size: 0.8rem;
    padding: 4px 8px;
  }
}