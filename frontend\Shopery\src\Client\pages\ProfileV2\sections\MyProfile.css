.profile-container {
  max-width: 700px;
  margin: 0 auto;
  padding: 20px;
  font-family: "Arial", sans-serif;
  color: #333;
}

.profile-title {
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: 700;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
}

.profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.profile-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #ddd;
  margin-right: 20px;
}

.profile-basic h4 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.profile-basic p {
  margin: 4px 0;
  color: #777;
}

.status-badge {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  margin-top: 5px;
  text-transform: capitalize;
}

.status-badge.verified {
  background-color: #d4f5d4;
  color: #2d8a34;
}

.status-badge.unverified {
  background-color: #ffe1e1;
  color: #c0392b;
}

.section-card {
  background: #fff;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.section-title {
  font-weight: 600;
  margin-bottom: 15px;
  font-size: 16px;
}

.form-row {
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
}

.form-row label {
  font-weight: 500;
  margin-bottom: 6px;
}

.form-row input {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 8px;
  outline: none;
  transition: border 0.3s;
}

.form-row input:focus {
  border-color: #007bff;
}

.form-row span {
  padding: 8px 0;
}

.actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

.btn-primary {
  padding: 10px 16px;
  background: #007bff;
  border: none;
  color: #fff;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  padding: 10px 16px;
  background: #e0e0e0;
  border: none;
  color: #333;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s;
}

.btn-secondary:hover {
  background: #cfcfcf;
}
