/* App Component Styles */
*{
  box-sizing: border-box;
}

html{
  box-sizing: border-box;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Loading Styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  gap: var(--spacing-md);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-light);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Admin Layout Styles */
.admin-layout {
  display: flex;
  min-height: 100vh;
}

.admin-main {
  flex: 1;
  margin-left: 250px;
  transition: margin-left var(--transition-normal);
}

.admin-main.sidebar-collapsed {
  margin-left: 70px;
}

.admin-content {
  padding: var(--spacing-lg);
  min-height: 100vh;
  background-color: var(--bg-secondary);
}

/* Client Layout Styles */
.client-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.client-main {
  flex: 1;
  padding-top: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .admin-main {
    margin-left: 0;
  }
  
  .admin-main.sidebar-collapsed {
    margin-left: 0;
  }
  
  .admin-content {
    padding: var(--spacing-md);
  }
}
