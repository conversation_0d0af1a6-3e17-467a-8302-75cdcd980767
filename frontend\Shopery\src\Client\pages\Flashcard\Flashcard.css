/* Client/pages/Flashcard/Flashcard.css */
.flashcard-page {
  min-height: 100vh;
  background: #f9fafb;
  padding: 24px 0;
}

.flashcard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.flashcard-header {
  margin-bottom: 32px;
}

.flashcard-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #111827;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.flashcard-header h1::before {
  content: '';
  width: 24px;
  height: 24px;
  background: #3b82f6;
  border-radius: 4px;
  position: relative;
}

.flashcard-header h1::after {
  content: '';
  width: 16px;
  height: 16px;
  background: white;
  border-radius: 2px;
  position: absolute;
  top: 4px;
  left: 4px;
}

/* Info Banner */
.info-banner {
  background: #ecfdf5;
  border: 1px solid #a7f3d0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 32px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.info-icon {
  width: 20px;
  height: 20px;
  background: #10b981;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
  margin-top: 2px;
}

.info-banner p {
  margin: 0;
  color: #065f46;
  font-size: 14px;
  line-height: 1.5;
}

/* Sections */
.topics-section {
  margin-bottom: 40px;
}

.topics-section h3 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 16px 0;
}

.empty-learning {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  color: #6b7280;
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 24px;
}

/* Topics Grid - 4 cột */
.topics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 32px;
}

/* Loading & Error */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p,
.error-container p {
  color: #6b7280;
  font-size: 16px;
  margin: 0;
}

.error-container p {
  color: #ef4444;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 32px;
}

.pagination-btn {
  width: 40px;
  height: 40px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  transition: all 0.2s ease;
}

.pagination-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.pagination-btn.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* Responsive */
@media (max-width: 1200px) {
  .topics-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 900px) {
  .topics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .flashcard-container {
    padding: 0 16px;
  }
  
  .flashcard-header h1 {
    font-size: 24px;
  }
  
  .topics-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .info-banner {
    padding: 12px;
  }
  
  .info-banner p {
    font-size: 13px;
  }
}