/* Exam Stats Page Styles */
.exam-stats {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 2rem 0;
}

.exam-stats__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Loading State */
.exam-stats-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  color: #181818;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #E5E7EB;
  border-top: 4px solid #1F2937;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Header */
.exam-stats__header {
  text-align: center;
  margin-bottom: 3rem;
  color: #181818;
}

.exam-stats__header h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #181818;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.exam-stats__header p {
  font-size: 1.25rem;
  color: #888;
}

/* Overview Cards */
.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  color: #181818;
}

.stat-content h3 {
  color: #181818;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

.stat-value {
  color: #181818;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.stat-subtitle {
  color: #888;
  font-size: 0.75rem;
}

/* Performance Section */
.performance-section {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 3rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.performance-section h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #181818;
  margin-bottom: 2rem;
}

.performance-chart {
  height: 300px;
  position: relative;
}

.chart-container {
  height: 100%;
  display: flex;
  align-items: end;
  gap: 1rem;
  padding: 1rem 0;
}

.chart-bars {
  display: flex;
  align-items: end;
  gap: 1rem;
  width: 100%;
  height: 200px;
}

.chart-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  position: relative;
}

.bar-fill {
  width: 100%;
  min-height: 20px;
  border-radius: 4px 4px 0 0;
  transition: height 0.3s ease;
  position: relative;
}

.bar-label {
  font-size: 0.75rem;
  color: #888;
  margin-top: 0.5rem;
  text-align: center;
}

.bar-value {
  position: absolute;
  top: -25px;
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
}

/* Section Performance */
.section-performance {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 3rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.section-performance h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #181818;
  margin-bottom: 2rem;
}

.sections-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.section-card {
  background: #F9FAFB;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #E5E7EB;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #181818;
  margin: 0;
}

.section-score {
  font-size: 1.5rem;
  font-weight: 700;
  color: #181818;
}

.section-details {
  margin-bottom: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  font-size: 0.875rem;
  color: #888;
}

.detail-item span:first-child {
  font-weight: 500;
}

.section-progress {
  margin-top: 1rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #E5E7EB;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
}

/* Test Type Performance */
.test-type-performance {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 3rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.test-type-performance h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #181818;
  margin-bottom: 2rem;
}

.test-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.test-type-card {
  background: #F9FAFB;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #E5E7EB;
  text-align: center;
}

.type-header {
  margin-bottom: 1rem;
}

.type-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #181818;
  margin: 0 0 0.5rem 0;
}

.type-count {
  font-size: 0.875rem;
  color: #888;
}

.type-score {
  margin-bottom: 1rem;
}

.score-value {
  font-size: 2rem;
  font-weight: 700;
  color: #181818;
  display: block;
}

.score-label {
  font-size: 0.875rem;
  color: #888;
}

.type-progress {
  margin-top: 1rem;
}

/* Recent Tests */
.recent-tests {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.recent-tests__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.recent-tests__header h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #181818;
  margin: 0;
}

.view-all-btn {
  color: #181818;
  text-decoration: none;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: background 0.3s ease;
}

.view-all-btn:hover {
  background: #4fd1c7;
  color: white;
}

.recent-tests__list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recent-test-item {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: 1rem;
  align-items: center;
  padding: 1.5rem;
  background: #F9FAFB;
  border-radius: 12px;
  border: 1px solid #E5E7EB;
  transition: transform 0.3s ease;
}

.recent-test-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.test-info h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #181818;
  margin: 0 0 0.5rem 0;
}

.test-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #888;
}

.test-type {
  background: #1F2937;
  color: #181818;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.75rem;
}

.test-score {
  text-align: center;
}

.score-display {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
}

.score-value {
  font-size: 1.5rem;
  font-weight: 700;
}

.score-max {
  font-size: 1rem;
  color: #888;
}

.in-progress {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #F59E0B;
  font-weight: 600;
}

.progress-dot {
  width: 8px;
  height: 8px;
  background: #F59E0B;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.test-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-view-result,
.btn-continue {
  padding: 0.5rem 1rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.btn-view-result {
  background: #4fd1c7;
  color: white;
}

.btn-view-result:hover {
  background: #38b2ac;
  transform: translateY(-1px);
}

.btn-continue {
  background: #10B981;
  color: #181818;
}

.btn-continue:hover {
  background: #059669;
  transform: translateY(-1px);
}

/* Responsive */
@media (max-width: 768px) {
  .exam-stats__container {
    padding: 0 0.5rem;
  }

  .exam-stats__header h1 {
    font-size: 2rem;
  }

  .stats-overview {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .sections-grid,
  .test-types-grid {
    grid-template-columns: 1fr;
  }

  .recent-test-item {
    grid-template-columns: 1fr;
    gap: 1rem;
    text-align: center;
  }

  .test-actions {
    justify-content: center;
  }

  .chart-bars {
    gap: 0.5rem;
  }

  .bar-label {
    font-size: 0.625rem;
  }
}
