/* Home Page - CSS theo chuẩn BEM */

.home {
  min-height: 100vh;
  background: linear-gradient(135deg, #4fd1c7 0%, #38b2ac 100%);
  position: relative;
  overflow: hidden;
}

/* Hero Section */
.hero {
  padding-top: 70px; /* Space for fixed header */
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
}

.hero__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  min-height: calc(100vh - 70px);
}

/* Left Content */
.hero__content {
  color: white;
  z-index: 2;
}

.hero__title {
  font-size: 48px;
  font-weight: 700;
  line-height: 1.2;
  margin: 0 0 24px 0;
  color: white;
}

.hero__title-highlight {
  color: #FF6B35;
  position: relative;
}

.hero__title-highlight::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 3px;
  background: #FF6B35;
  border-radius: 2px;
}

.hero__description {
  font-size: 18px;
  line-height: 1.6;
  margin: 0 0 40px 0;
  color: rgba(255, 255, 255, 0.9);
  max-width: 500px;
}

.hero__actions {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.hero__btn {
  padding: 16px 32px;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.hero__btn--primary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.hero__btn--primary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.hero__play-section {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.hero__play-section:hover {
  transform: translateY(-1px);
}

.hero__play-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.hero__play-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.hero__play-text {
  color: white;
  font-weight: 500;
  font-size: 14px;
}

/* Right Content - Visual Section */
.hero__visual {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.hero__image-container {
  position: relative;
  z-index: 1;
  margin-top: 60px; /* Đẩy hình ảnh xuống dưới */
}

.hero__image {
  position: relative;
  width: 400px;
  height: 500px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  border: 3px solid rgba(255, 255, 255, 0.3); /* Thêm viền */
  background: rgba(255, 255, 255, 0.1); /* Thêm background nhẹ */
}

.hero__image-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center top; /* Tập trung vào phần đầu của ảnh */
  transition: transform 0.3s ease;
}

.hero__image:hover .hero__image-img {
  transform: scale(1.05);
}

/* Floating Cards - Nằm trên hình ảnh */
.hero__floating-cards {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 2;
}

.floating-card {
  position: absolute;
  background: white;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  animation: float 6s ease-in-out infinite;
  pointer-events: auto;
  cursor: pointer;
  transition: all 0.3s ease;
}

.floating-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.floating-card--top-left {
  top: 10%;
  left: -20px;
  animation-delay: 0s;
}

.floating-card--top-right {
  top: 5%;
  right: -30px;
  width: 40px;
  height: 40px;
  padding: 8px;
  background: #FF6B35;
  animation-delay: 1s;
}

.floating-card--middle-right {
  top: 40%;
  right: -40px;
  animation-delay: 2s;
}

.floating-card--bottom-right {
  bottom: 30%; /* Điều chỉnh vị trí */
  left: -20px; /* Thay đổi từ right sang left để thẳng với card đầu tiên */
  animation-delay: 3s;
}

.floating-card__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.floating-card__icon--stats {
  margin-bottom: 0;
}

.floating-card__content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.floating-card__text {
  font-size: 12px;
  font-weight: 600;
  color: #2D3748;
  line-height: 1.3;
}

.floating-card__time {
  font-size: 10px;
  color: #718096;
  font-weight: 500;
}

.floating-card__join-btn {
  background: #FF6B35;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 4px;
}

.floating-card__join-btn:hover {
  background: #E55A2B;
  transform: translateY(-1px);
}

.floating-card__avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 8px;
}

.floating-card__avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Floating Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Curved Bottom */
.home__curve {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 200px;
  z-index: 1;
}

.home__curve svg {
  width: 100%;
  height: 100%;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero__container {
    gap: 40px;
  }

  .hero__title {
    font-size: 40px;
  }

  .hero__image {
    width: 350px;
    height: 450px;
  }

  .floating-card--bottom-right {
    left: -40px;
  }
}

@media (max-width: 768px) {
  .hero__container {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .hero__title {
    font-size: 32px;
  }

  .hero__description {
    font-size: 16px;
  }

  .hero__actions {
    justify-content: center;
  }

  .hero__image {
    width: 300px;
    height: 400px;
  }

  .floating-card--top-left {
    left: 10px;
  }

  .floating-card--top-right {
    right: 10px;
  }

  .floating-card--middle-right {
    right: 10px;
  }

  .floating-card--bottom-right {
    left: 10px; /* Điều chỉnh cho mobile */
  }
}

@media (max-width: 480px) {
  .hero__container {
    padding: 0 16px;
  }

  .hero__title {
    font-size: 28px;
  }

  .hero__description {
    font-size: 14px;
  }

  .hero__btn {
    padding: 12px 24px;
    font-size: 14px;
  }

  .hero__image {
    width: 250px;
    height: 350px;
  }

  .floating-card {
    padding: 12px;
  }

  .floating-card__text {
    font-size: 10px;
  }
  .floating-card--bottom-right {
    left: -10px; /* Điều chỉnh cho mobile */
  }
}
